import { addUserCredit, addUserCreditBalance, updateUserCreditByClerkId, addSubscription, addSubscriptionRenewal, updateSubscription } from "@/actions/credits";
import {findUserCreditsByUserId} from "@/actions/user";
import { getProductInfoFromInvoice, getProductInfoFromPrice, cancelSubscription } from "@/lib/stripe-client";
import { IUserCredit } from '@/types/user-credit';

const stripe_variantids_map = new Map<string,number>([
    ['price_1RqqKXFPMGqYeSEe3K156dEV',1],
    ['price_1RsKYhFPMGqYeSEeycT1dE19',2],
    ['price_1RsKZEFPMGqYeSEenjGeulo0',3],
]);


export async function createOrderDetailsFromStripe (orderDetails: IOrderDetail) {
    console.log('=== 开始处理 Stripe 支付成功 ===');
    console.log('订单详情:', JSON.stringify(orderDetails, null, 2));
    console.log('是否为续费:', orderDetails.isRenewal || false);

    const product = await getProductInfo(orderDetails);

    if(!product){
        console.error('获取产品信息失败');
        return;
    }
    console.log('产品信息:', JSON.stringify(product, null, 2));
    
    let creditAmount = Number(stripe_variantids_map.get(product.priceId as string));
    if(!creditAmount){
        console.error('价格ID映射未找到:', product.priceId);
        console.log('可用的价格映射:', Array.from(stripe_variantids_map.entries()));
        return;
    }
    console.log('积分数量:', creditAmount);
    
    let credit_desc = '';
    let credit_type = '';
    let isSubscription = false;
    
    if (product.subscriptionId) {
        credit_desc = 'subscription: ' + product.priceName + ', ' + product.priceId;
        credit_type = '1';
        isSubscription = true;
        console.log('识别为订阅支付');
    } else {
        credit_desc = 'one time: ' + product.priceName + ', ' + product.priceId;
        credit_type = '2';
        console.log('识别为一次性支付');
    }
    
    const user_id = orderDetails.userId ?? product.userId;
    console.log('用户ID:', user_id);
    
    if (!user_id) {
        console.error('用户ID为空，无法处理支付');
        return;
    }
    
    const credit: IUserCredit = {
        user_id: user_id,
        order_number: orderDetails.transactionId,
        credit_amount: creditAmount,
        credit_type: credit_type,
        credit_transaction_type: '1',
        credit_desc: credit_desc,
        order_price: orderDetails.price,
        order_date: orderDetails.date
    }
    console.log('积分记录:', JSON.stringify(credit, null, 2));
    
    try {
        const creditResult = await addUserCredit(credit);
        console.log('积分记录添加结果:', creditResult);
        
        if (creditResult === 0) {
            console.log('积分记录添加失败或为重复订单，跳过余额更新');
            return;
        }
        
        const balanceResult = await addUserCreditBalance(creditAmount, user_id);
        console.log('用户积分余额更新结果:', balanceResult, '增加积分:', creditAmount);
        
        if (balanceResult === 0) {
            console.error('用户积分余额更新失败');
        }
        
        if (isSubscription && product.subscriptionId) {
            credit.subscriptionId = product.subscriptionId;

            // 如果是续费，使用特殊的续费处理逻辑
            const subscriptionResult = orderDetails.isRenewal ?
                await addSubscriptionRenewal(credit) :
                await addSubscription(credit);

            console.log('订阅记录添加结果:', subscriptionResult, '(续费:', orderDetails.isRenewal || false, ')');

            if (subscriptionResult === 0) {
                console.error('订阅记录添加失败 - 重复的subscription_id');
            } else if (subscriptionResult === -1) {
                if (orderDetails.isRenewal) {
                    console.error('续费处理失败 - 这不应该发生');
                } else {
                    console.error('订阅记录添加失败 - 用户已有活跃订阅');
                    // 这种情况下可能需要退款或其他处理
                    // 但由于webhook已经处理了支付，这里主要是记录日志
                }
            }
        }
        
        console.log('=== Stripe 支付处理完成 ===');
    } catch (error) {
        console.error('支付处理过程中出错:', error);
        throw error;
    }
}

export async function refundedOrderDetailsFromStripe (orderDetails: IOrderDetail) {


    const product = await getProductInfo(orderDetails);
    
    if(!product){
        return;
    }

    const variant_id = product.priceId;

    let creditAmount = Number(stripe_variantids_map.get(variant_id));
    if(!creditAmount){
        return;
    }

    const variant_name = product.priceName;

    const user_id = orderDetails.userId??product.userId;
    
    const credit:IUserCredit ={
        user_id: user_id,
        order_number: orderDetails.transactionId,
        credit_amount: creditAmount,
        credit_type: '3',
        credit_transaction_type: '3',
        credit_desc: 'refund: '+variant_name+', '+variant_id,
        order_price: orderDetails.price,
        order_date: orderDetails.date
    }
    const currentCredit = await findUserCreditsByUserId(user_id);
    await addUserCredit(credit);
    await updateUserCreditByClerkId(creditAmount,user_id,currentCredit);
}

export async function subscriptionCancelledFromStripe (subscriptionId: string) {
    await updateSubscription(subscriptionId);
}

async function getProductInfo(orderDetails: IOrderDetail){
    console.log('=== getProductInfo 输入参数 ===');
    console.log('invoice:', orderDetails.invoice);
    console.log('priceId:', orderDetails.priceId);
    console.log('customerId:', orderDetails.customerId);
    
    // 如果有发票ID（订阅支付或一次性支付的发票），优先使用发票获取产品信息
    if(orderDetails.invoice){
        console.log('使用发票ID获取产品信息');
        return await getProductInfoFromInvoice(orderDetails.invoice);
    }

    // 如果没有发票ID但有价格ID，使用价格ID获取产品信息（传递customerId用于订阅查找）
    if(orderDetails.priceId){
        console.log('使用价格ID获取产品信息');
        return await getProductInfoFromPrice(orderDetails.priceId, orderDetails.customerId);
    }

    console.log('既没有发票ID也没有价格ID，无法获取产品信息');
    return undefined;
}

export async function cancelSubscriptionById( subscriptionId: string ){
    return await cancelSubscription(subscriptionId);
}


export interface IOrderDetail {
    userId: string;
    transactionId: string;
    invoice: string;
    priceId: string;
    price: number;
    date: string;
    customerId?: string;
    isRenewal?: boolean; // 标记是否为续费
}