@tailwind base;
@tailwind components;
@tailwind utilities;

/* 2024年现代设计系统 - 支持亮暗模式切换 */

@layer base {
  :root {
    /* 亮色模式 - 瘦身健康主题 */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    /* 玻璃态组件系统 */
    --glass-bg: 0 0% 100% / 0.8;
    --glass-border: 0 0% 0% / 0.1;
    
    /* 瘦身健康主题色彩系统 */
    --primary: 210 100% 50%;     /* 医疗蓝 - 主色 */
    --primary-foreground: 0 0% 100%;

    --secondary: 158 64% 52%;    /* 健康绿 - 辅色 */
    --secondary-foreground: 0 0% 100%;

    --accent: 12 100% 50%;       /* 心脏红 - 强调色 */
    --accent-foreground: 0 0% 100%;

    /* 现代中性色调 */
    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --border: 0 0% 89%;
    --input: 0 0% 89%;
    --ring: 210 100% 50%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --radius: 1rem;
  }

  .dark {
    /* 深色模式 - 瘦身健康主题 */
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;

    /* 玻璃态组件系统 - 深色模式 */
    --glass-bg: 224 71% 4% / 0.4;
    --glass-border: 216 34% 17% / 0.2;
    
    --primary: 210 100% 56%;     /* 亮医疗蓝 */
    --primary-foreground: 222.2 84% 4.9%;
    
    --secondary: 158 64% 52%;    /* 亮健康绿 */
    --secondary-foreground: 222.2 84% 4.9%;

    --accent: 12 100% 63%;       /* 亮心脏红 */
    --accent-foreground: 222.2 84% 4.9%;

    --muted: 216 34% 17%;
    --muted-foreground: 216 34% 65%;

    --card: 224 71% 4%;
    --card-foreground: 213 31% 91%;

    --popover: 224 71% 4%;
    --popover-foreground: 213 31% 91%;

    --border: 216 34% 17%;
    --input: 216 34% 17%;
    --ring: 216 34% 17%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground antialiased;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* 亮色模式背景 - 瘦身健康主题 */
  body {
    background: 
      /* 医疗纹理效果 */
      radial-gradient(circle at 20% 30%, hsla(210, 100%, 85%, 0.05) 0%, transparent 25%),
      radial-gradient(circle at 80% 70%, hsla(158, 64%, 75%, 0.04) 0%, transparent 30%),
      radial-gradient(circle at 40% 80%, hsla(12, 100%, 85%, 0.03) 0%, transparent 20%),
      radial-gradient(circle at 60% 20%, hsla(210, 100%, 75%, 0.04) 0%, transparent 25%),
      /* 医疗网格纹理层 */
      linear-gradient(45deg, transparent 49%, hsla(210, 100%, 95%, 0.02) 50%, transparent 51%),
      linear-gradient(-45deg, transparent 49%, hsla(158, 64%, 95%, 0.02) 50%, transparent 51%),
      /* 主背景 */
      linear-gradient(135deg, 
        hsl(210, 40%, 99%) 0%, 
        hsl(0, 0%, 99%) 30%,
        hsl(158, 25%, 98%) 60%,
        hsl(210, 30%, 98%) 100%);
    min-height: 100vh;
  }

  /* 深色模式背景 - 瘦身健康主题 */
  .dark body {
    background: 
      /* 医疗纹理效果 */
      radial-gradient(circle at 20% 30%, hsla(210, 100%, 50%, 0.08) 0%, transparent 25%),
      radial-gradient(circle at 80% 70%, hsla(158, 64%, 52%, 0.06) 0%, transparent 30%),
      radial-gradient(circle at 40% 80%, hsla(12, 100%, 50%, 0.05) 0%, transparent 20%),
      radial-gradient(circle at 60% 20%, hsla(210, 100%, 60%, 0.07) 0%, transparent 25%),
      /* 医疗网格纹理层 */
      linear-gradient(45deg, transparent 49%, hsla(210, 100%, 20%, 0.03) 50%, transparent 51%),
      linear-gradient(-45deg, transparent 49%, hsla(158, 64%, 20%, 0.03) 50%, transparent 51%),
      /* 主背景 */
      linear-gradient(135deg, 
        hsl(224, 71%, 3%) 0%, 
        hsl(224, 71%, 4%) 30%,
        hsl(216, 34%, 6%) 60%,
        hsl(224, 71%, 3%) 100%);
  }

  /* 现代滚动条 */
  ::-webkit-scrollbar {
    width: 6px;
  }
  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }
  ::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, hsl(var(--primary)), hsl(var(--secondary)));
    border-radius: 3px;
  }
}

@layer components {
  /* 玻璃态效果 - 支持亮暗模式 */
  .glass-card {
    @apply relative backdrop-blur-xl border rounded-2xl;
    background: hsla(var(--glass-bg));
    border-color: hsla(var(--glass-border));
    box-shadow: 
      0 8px 32px 0 rgba(0, 0, 0, 0.1),
      inset 0 1px 0 0 rgba(255, 255, 255, 0.1);
  }

  .dark .glass-card {
    box-shadow: 
      0 8px 32px 0 rgba(0, 0, 0, 0.37),
      inset 0 1px 0 0 rgba(255, 255, 255, 0.05);
  }

  .glass-button {
    @apply relative overflow-hidden px-6 py-3 rounded-2xl font-medium transition-all duration-300 border;
    background: linear-gradient(135deg, 
      hsla(210, 100%, 50%, 0.1) 0%, 
      hsla(158, 64%, 52%, 0.1) 100%);
    border-color: hsla(210, 100%, 50%, 0.2);
    backdrop-filter: blur(10px);
  }

  .glass-button:hover {
    transform: translateY(-2px);
    box-shadow: 
      0 20px 40px -12px hsla(210, 100%, 50%, 0.2),
      0 0 0 1px hsla(210, 100%, 50%, 0.3);
  }

  /* 医疗主题脉动动画 */
  .pulse-slow {
    animation: medical-pulse 3s ease-in-out infinite;
  }

  @keyframes medical-pulse {
    0%, 100% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
    }
    50% {
      transform: scale(1.05);
      box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
    }
  }

  /* 心跳动画 */
  .heartbeat {
    animation: heartbeat 1.5s ease-in-out infinite;
  }

  @keyframes heartbeat {
    0%, 50%, 100% {
      transform: scale(1);
    }
    25% {
      transform: scale(1.1);
    }
    75% {
      transform: scale(0.95);
    }
  }

  /* 现代流体渐变 - 瘦身健康主题 */
  .gradient-text {
    @apply bg-clip-text text-transparent;
    background: linear-gradient(135deg,
      hsl(210, 100%, 50%) 0%,
      hsl(158, 64%, 52%) 50%,
      hsl(12, 100%, 50%) 100%);
    background-size: 200% 200%;
    animation: gradient-shift 3s ease-in-out infinite;
  }

  /* 纯文字渐变 - 无背景 */
  .text-gradient-only {
    background: linear-gradient(135deg,
      hsl(210, 100%, 50%) 0%,
      hsl(158, 64%, 52%) 50%,
      hsl(12, 100%, 50%) 100%);
    background-size: 200% 200%;
    animation: gradient-shift 3s ease-in-out infinite;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }

  /* 静态文字渐变 - 无动画 */
  .text-gradient-static {
    background: linear-gradient(135deg,
      hsl(210, 100%, 50%) 0%,
      hsl(158, 64%, 52%) 50%,
      hsl(12, 100%, 50%) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }

  /* 绿-黄渐变文字 - 健康主题 */
  .text-gradient-green-yellow {
    background: linear-gradient(135deg,
      hsl(158, 64%, 45%) 0%,
      hsl(45, 90%, 55%) 50%,
      hsl(210, 100%, 50%) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }

  /* 绿-黄渐变文字带动画 */
  .text-gradient-green-yellow-animated {
    background: linear-gradient(135deg,
      hsl(158, 64%, 45%) 0%,
      hsl(45, 90%, 55%) 50%,
      hsl(210, 100%, 50%) 100%);
    background-size: 200% 200%;
    animation: gradient-shift 3s ease-in-out infinite;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }

  /* 深色模式文字渐变 */
  .dark .gradient-text {
    background: linear-gradient(135deg,
      hsl(210, 100%, 65%) 0%,
      hsl(158, 64%, 65%) 50%,
      hsl(12, 100%, 65%) 100%);
  }

  /* 白色文字上的渐变背景 */
  .gradient-bg-white-text {
    @apply text-white font-bold;
    background: linear-gradient(135deg,
      hsl(210, 100%, 50%) 0%,
      hsl(158, 64%, 52%) 50%,
      hsl(12, 100%, 50%) 100%);
    background-size: 200% 200%;
    animation: gradient-shift 3s ease-in-out infinite;
    padding: 1rem 2rem;
    border-radius: 1rem;
  }

  /* 医疗主题渐变标题 */
  .gradient-title-primary {
    background: linear-gradient(135deg,
      hsl(210, 100%, 50%) 0%,
      hsl(210, 100%, 40%) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }

  .gradient-title-secondary {
    background: linear-gradient(135deg,
      hsl(158, 64%, 52%) 0%,
      hsl(158, 64%, 42%) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }

  .gradient-title-accent {
    background: linear-gradient(135deg,
      hsl(12, 100%, 50%) 0%,
      hsl(12, 100%, 40%) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }

  /* 医疗主题色彩渐变 */
  .gradient-medical {
    background: linear-gradient(135deg, #3b82f6 0%, #10b981 50%, #ef4444 100%);
  }

  .gradient-health {
    background: linear-gradient(135deg, #06b6d4 0%, #10b981 100%);
  }

  .gradient-vitals {
    background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
  }

  .gradient-care {
    background: linear-gradient(135deg, #f59e0b 0%, #ef4444 100%);
  }

  .gradient-wellness {
    background: linear-gradient(135deg, #10b981 0%, #06b6d4 100%);
  }

  .gradient-diagnostic {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  }

  @keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  .modern-card {
    @apply bg-white/80 backdrop-blur-xl border border-white/50 rounded-2xl p-6 shadow-lg transition-all duration-300;
  }

  .modern-card:hover {
    @apply shadow-2xl -translate-y-2;
    transform: translateY(-8px);
  }

  .dark .modern-card:hover {
    @apply shadow-2xl;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  }

  /* 医疗主题按钮 */
  .btn-modern {
    @apply relative overflow-hidden px-8 py-4 rounded-2xl font-semibold transition-all duration-300 border-0;
    background: linear-gradient(135deg, 
      hsl(210, 100%, 50%) 0%, 
      hsl(210, 100%, 45%) 100%);
    color: white;
    box-shadow: 0 8px 25px -8px hsla(210, 100%, 50%, 0.5);
  }

  .btn-modern:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px -10px hsla(210, 100%, 50%, 0.6);
  }

  .btn-secondary {
    @apply relative overflow-hidden px-8 py-4 rounded-2xl font-semibold transition-all duration-300;
    background: white;
    color: hsl(210, 100%, 50%);
    border: 2px solid hsl(210, 100%, 50%);
    backdrop-filter: blur(10px);
  }

  .btn-secondary:hover {
    background: hsl(210, 100%, 50%);
    color: white;
    transform: translateY(-2px);
  }

  /* 健康主题按钮 */
  .btn-health {
    @apply relative overflow-hidden px-8 py-4 rounded-2xl font-semibold transition-all duration-300 border-0;
    background: linear-gradient(135deg, 
      hsl(158, 64%, 52%) 0%, 
      hsl(158, 64%, 47%) 100%);
    color: white;
    box-shadow: 0 8px 25px -8px hsla(158, 64%, 52%, 0.5);
  }

  .btn-health:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px -10px hsla(158, 64%, 52%, 0.6);
  }

  /* 急救主题按钮 */
  .btn-emergency {
    @apply relative overflow-hidden px-8 py-4 rounded-2xl font-semibold transition-all duration-300 border-0;
    background: linear-gradient(135deg, 
      hsl(12, 100%, 50%) 0%, 
      hsl(12, 100%, 45%) 100%);
    color: white;
    box-shadow: 0 8px 25px -8px hsla(12, 100%, 50%, 0.5);
  }

  .btn-emergency:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px -10px hsla(12, 100%, 50%, 0.6);
  }

  .modern-input {
    @apply bg-white/80 backdrop-blur-xl border border-white/50 rounded-xl px-4 py-3 transition-all duration-300 focus:ring-2 focus:ring-blue-500/50;
  }

  .modern-nav {
    @apply bg-white/80 backdrop-blur-xl border-b border-white/50;
    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.05);
  }

  /* 医疗主题浮动动画 */
  .float-animation {
    animation: medical-float 6s ease-in-out infinite;
  }

  @keyframes medical-float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
  }

  .float-delayed {
    animation: medical-float 6s ease-in-out infinite 2s;
  }

  /* 医疗主题图标样式 */
  .icon-modern {
    @apply w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300;
    background: linear-gradient(135deg,
      hsla(210, 100%, 50%, 0.1) 0%, 
      hsla(158, 64%, 52%, 0.1) 100%);
  }

  .icon-primary {
    @apply w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300;
    background: linear-gradient(135deg,
      hsla(210, 100%, 50%, 0.1) 0%, 
      hsla(210, 100%, 45%, 0.1) 100%);
  }

  .icon-secondary {
    @apply w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300;
    background: linear-gradient(135deg,
      hsla(158, 64%, 52%, 0.1) 0%, 
      hsla(158, 64%, 47%, 0.1) 100%);
  }

  .icon-accent {
    @apply w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300;
    background: linear-gradient(135deg,
      hsla(12, 100%, 50%, 0.1) 0%, 
      hsla(12, 100%, 45%, 0.1) 100%);
  }

  /* 医疗专业图标样式 */
  .icon-medical {
    @apply w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300;
    background: linear-gradient(135deg,
      hsla(210, 100%, 95%, 1) 0%, 
      hsla(210, 100%, 90%, 1) 100%);
  }

  .icon-health {
    @apply w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300;
    background: linear-gradient(135deg,
      hsla(158, 64%, 95%, 1) 0%, 
      hsla(158, 64%, 90%, 1) 100%);
  }

  .icon-vitals {
    @apply w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300;
    background: linear-gradient(135deg,
      hsla(12, 100%, 95%, 1) 0%, 
      hsla(12, 100%, 90%, 1) 100%);
  }

  .icon-care {
    @apply w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300;
    background: linear-gradient(135deg,
      hsla(45, 90%, 95%, 1) 0%, 
      hsla(45, 90%, 90%, 1) 100%);
  }

  .icon-wellness {
    @apply w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300;
    background: linear-gradient(135deg,
      hsla(270, 50%, 95%, 1) 0%, 
      hsla(270, 50%, 90%, 1) 100%);
  }

  /* 现代分割线 */
  .divider-modern {
    @apply h-1 rounded-full;
    background: linear-gradient(135deg, 
      hsl(210, 100%, 50%) 0%, 
      hsl(158, 64%, 52%) 50%,
      hsl(12, 100%, 50%) 100%);
  }

  /* 对比文字 */
  .text-contrast {
    @apply text-slate-800 dark:text-slate-100;
  }

  /* 医疗主题背景装饰 */
  .bg-decoration {
    @apply opacity-20 transition-opacity duration-500;
  }

  .dark .bg-decoration {
    @apply opacity-10;
  }

  /* 医疗主题徽章 */
  .modern-badge {
    @apply relative overflow-hidden transition-all duration-300;
    border: 1px solid hsla(210, 100%, 50%, 0.2);
  }

  .modern-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -8px hsla(210, 100%, 50%, 0.3);
  }

  .modern-badge::before {
    content: '';
    @apply absolute inset-0 opacity-0 transition-opacity duration-300;
    background: linear-gradient(135deg, 
      hsla(210, 100%, 50%, 0.1) 0%, 
      hsla(158, 64%, 52%, 0.1) 100%);
  }

  .modern-badge:hover::before {
    @apply opacity-100;
  }

  /* 健康评估展示区域 */
  .health-showcase {
    @apply relative w-full h-40 rounded-3xl border border-white/50 overflow-hidden;
    background: linear-gradient(135deg,
      hsla(210, 100%, 50%, 0.05) 0%,
      hsla(158, 64%, 52%, 0.05) 50%,
      hsla(12, 100%, 50%, 0.05) 100%);
    background-size: 400% 400%;
    animation: health-gradient 8s ease-in-out infinite;
    backdrop-filter: blur(20px);
  }

  .dark .health-showcase {
    background: linear-gradient(135deg,
      hsla(210, 100%, 50%, 0.1) 0%,
      hsla(158, 64%, 52%, 0.1) 50%,
      hsla(12, 100%, 50%, 0.1) 100%);
    background-size: 400% 400%;
    animation: health-gradient 8s ease-in-out infinite;
  }

  @keyframes health-gradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  /* 健康类别标签 */
  .health-category {
    @apply px-4 py-2 rounded-full text-sm font-medium transition-all duration-300;
    background: linear-gradient(135deg,
      hsla(210, 100%, 50%, 0.1) 0%,
      hsla(158, 64%, 52%, 0.1) 100%);
    border: 1px solid hsla(210, 100%, 50%, 0.2);
    color: hsl(210, 100%, 40%);
  }

  .dark .health-category {
    background: linear-gradient(135deg,
      hsla(210, 100%, 50%, 0.2) 0%,
      hsla(158, 64%, 52%, 0.2) 100%);
    border: 1px solid hsla(210, 100%, 50%, 0.3);
    color: hsl(210, 100%, 60%);
  }

  /* 血压健康金字塔样式 */
  .pyramid-level {
    @apply relative;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .pyramid-content {
    @apply relative overflow-hidden rounded-2xl p-6 shadow-lg border-0;
    backdrop-filter: blur(10px);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  /* 金字塔层级宽度 - 创建金字塔形状 */
  .level-1 {
    width: 100%;
    margin: 0 auto;
  }

  .level-2 {
    width: 85%;
    margin: 0 auto;
  }

  .level-3 {
    width: 70%;
    margin: 0 auto;
  }

  .level-4 {
    width: 55%;
    margin: 0 auto;
  }

  .level-5 {
    width: 40%;
    margin: 0 auto;
  }

  /* 金字塔悬停效果 */
  .pyramid-level:hover {
    transform: translateY(-8px) scale(1.02);
    z-index: 10;
  }

  .pyramid-level:hover .pyramid-content {
    box-shadow: 
      0 25px 50px -12px rgba(0, 0, 0, 0.25),
      0 0 0 1px rgba(255, 255, 255, 0.1);
  }

  /* 金字塔层级特定样式 */
  .level-1:hover .pyramid-content {
    box-shadow: 
      0 25px 50px -12px rgba(239, 68, 68, 0.4),
      0 0 0 1px rgba(255, 255, 255, 0.2);
  }

  .level-2:hover .pyramid-content {
    box-shadow: 
      0 25px 50px -12px rgba(249, 115, 22, 0.4),
      0 0 0 1px rgba(255, 255, 255, 0.2);
  }

  .level-3:hover .pyramid-content {
    box-shadow: 
      0 25px 50px -12px rgba(59, 130, 246, 0.4),
      0 0 0 1px rgba(255, 255, 255, 0.2);
  }

  .level-4:hover .pyramid-content {
    box-shadow: 
      0 25px 50px -12px rgba(16, 185, 129, 0.4),
      0 0 0 1px rgba(255, 255, 255, 0.2);
  }

  .level-5:hover .pyramid-content {
    box-shadow: 
      0 25px 50px -12px rgba(34, 197, 94, 0.4),
      0 0 0 1px rgba(255, 255, 255, 0.2);
  }

  /* 金字塔连接线效果 */
  .pyramid-level::before {
    content: '';
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 12px;
    background: linear-gradient(180deg, 
      rgba(148, 163, 184, 0.5) 0%, 
      rgba(148, 163, 184, 0.2) 100%);
    border-radius: 1px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .pyramid-level:not(:first-child)::before {
    opacity: 1;
  }

  /* 金字塔动画效果 */
  .pyramid-level {
    animation: pyramid-fade-in 0.8s ease-out forwards;
    opacity: 0;
    transform: translateY(30px);
  }

  .level-1 { animation-delay: 0.1s; }
  .level-2 { animation-delay: 0.2s; }
  .level-3 { animation-delay: 0.3s; }
  .level-4 { animation-delay: 0.4s; }
  .level-5 { animation-delay: 0.5s; }

  @keyframes pyramid-fade-in {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 金字塔内容渐变覆盖层 */
  .pyramid-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%, 
      rgba(255, 255, 255, 0.05) 50%,
      rgba(0, 0, 0, 0.05) 100%);
    border-radius: inherit;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .pyramid-level:hover .pyramid-content::before {
    opacity: 1;
  }

  /* 金字塔脉动效果 */
  .pyramid-content {
    position: relative;
    overflow: hidden;
  }

  .pyramid-content::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.6s ease;
  }

  .pyramid-level:hover .pyramid-content::after {
    width: 100%;
    height: 100%;
    opacity: 0;
  }

  /* 进度指示器样式 */
  .pyramid-indicator {
    @apply p-3 rounded-xl transition-all duration-300;
    background: rgba(255, 255, 255, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
  }

  .pyramid-indicator:hover {
    @apply shadow-lg;
    background: rgba(255, 255, 255, 0.8);
    transform: translateX(4px);
  }

  .pyramid-progress {
    transform: scaleX(0);
    transform-origin: left;
    animation: progress-fill 2s ease-out forwards;
    animation-delay: 0.5s;
  }

  @keyframes progress-fill {
    from {
      transform: scaleX(0);
    }
    to {
      transform: scaleX(1);
    }
  }

  /* 进度条发光效果 */
  .pyramid-progress {
    position: relative;
    overflow: hidden;
  }

  .pyramid-progress::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
      transparent 0%, 
      rgba(255, 255, 255, 0.4) 50%, 
      transparent 100%);
    animation: progress-shine 2s ease-in-out infinite;
    animation-delay: 2s;
  }

  @keyframes progress-shine {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }

  }

  /* 健康评分动画 */
  .health-score {
    animation: score-count-up 2s ease-out forwards;
    animation-delay: 1s;
  }

  @keyframes score-count-up {
    from {
      transform: scale(0.8);
      opacity: 0;
    }
    to {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* 金字塔交互同步效果 */
  .pyramid-indicator:hover ~ .pyramid-level .pyramid-content,
  .pyramid-level:hover ~ .pyramid-indicator {
    transform: scale(1.02);
  }

  /* 响应式金字塔 */
  @media (max-width: 768px) {
    .pyramid-content {
      @apply p-4;
    }

    .level-1 { width: 100%; }
    .level-2 { width: 90%; }
    .level-3 { width: 80%; }
    .level-4 { width: 70%; }
    .level-5 { width: 60%; }

    .pyramid-level:hover {
      transform: translateY(-4px) scale(1.01);
    }

    .pyramid-indicator {
      @apply p-2;
    }

    .pyramid-indicator:hover {
      transform: translateX(2px);
    }
  }

  @media (max-width: 480px) {
    .pyramid-content {
      @apply p-3;
    }

    .pyramid-content h3 {
      @apply text-base;
    }

    .pyramid-content p {
      @apply text-xs;
    }

    .level-1, .level-2, .level-3, .level-4, .level-5 {
      width: 100%;
    }

    /* 移动端隐藏进度指示器的详细数据 */
    .pyramid-content .grid {
      @apply hidden;
    }
  }
}

/* 响应式设计优化 */
@media (max-width: 375px) {
  .glass-button {
    @apply px-4 py-2 text-sm;
  }

  .btn-modern {
    @apply px-6 py-3 text-sm;
  }

  .modern-card {
    @apply p-4;
  }

  .modern-nav {
    @apply px-2;
  }

  /* 小屏幕文字渐变优化 */
  .gradient-text {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
  }

  .modern-card:hover {
    transform: translateY(-4px);
  }

  .bg-decoration {
    @apply text-2xl;
  }
}

@media (max-width: 480px) {
  .glass-card {
    @apply p-4 rounded-xl;
  }

  .modern-card:hover {
    transform: translateY(-4px);
  }
}

/* 动画性能优化 */
@media (prefers-reduced-motion: reduce) {
  .float-animation,
  .float-delayed,
  .pulse-slow,
  .heartbeat {
    animation: none;
  }

  .modern-card,
  .glass-button,
  .btn-modern,
  .btn-secondary,
  .btn-health,
  .btn-emergency {
    transition: none;
  }
}

/* 高对比度支持 */
@media (prefers-contrast: high) {
  .glass-card {
    @apply border-2 border-slate-300;
  }

  .gradient-text {
    @apply text-slate-800;
    background: none;
    -webkit-text-fill-color: currentColor;
  }
}

/* 隐藏滚动条但保持可滚动 */
.scrollbar-none {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scrollbar-none::-webkit-scrollbar {
  display: none;
}

/* 3D滚轮效果 */
.snap-y.snap-mandatory {
  scroll-snap-type: y mandatory;
}

.snap-center {
  scroll-snap-align: center;
}

/* 平滑滚动 */
.smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* 日期选择器容器 */
.date-picker-container {
  perspective: 1000px;
  transform-style: preserve-3d;
}

.date-picker-wheel {
  transform: rotateX(45deg);
  transform-origin: center;
}



/* 新增动画效果 */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

.bg-grid-slate-100 {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='rgb(148 163 184 / 0.05)'%3e%3cpath d='m0 .5h32m-32 32v-32'/%3e%3c/svg%3e");
}

/* 高级卡片动画效果 */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes float-delayed {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-15px); }
}

@keyframes pulse-slow {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes bounce-gentle {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

@keyframes scale-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes glow-pulse {
  0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
  50% { box-shadow: 0 0 40px rgba(59, 130, 246, 0.6); }
}

/* 3D 透视效果 */
.perspective-1000 {
  perspective: 1000px;
}

.rotate-y-2 {
  transform: rotateY(2deg);
}

/* 高级阴影效果 */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* 玻璃态效果增强 */
.backdrop-blur-2xl {
  backdrop-filter: blur(40px);
}

/* 高级渐变动画 */
@keyframes gradient-xy {
  0%, 100% {
    background-size: 400% 400%;
    background-position: left center;
  }
  50% {
    background-size: 200% 200%;
    background-position: right center;
  }
}

.animate-gradient-xy {
  animation: gradient-xy 6s ease infinite;
}

/* 粒子动画效果 */
@keyframes particle-float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  33% {
    transform: translateY(-10px) rotate(120deg);
    opacity: 1;
  }
  66% {
    transform: translateY(-5px) rotate(240deg);
    opacity: 0.8;
  }
}

.animate-particle-float {
  animation: particle-float 4s ease-in-out infinite;
}

/* 高级悬停效果 */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* 浮动动画类 */
.float-animation {
  animation: float 3s ease-in-out infinite;
}

.float-delayed {
  animation: float-delayed 4s ease-in-out infinite;
}

.pulse-slow {
  animation: pulse-slow 2s ease-in-out infinite;
}

/* 高级标题效果 */
@keyframes title-glow {
  0%, 100% {
    text-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  }
  50% {
    text-shadow: 0 0 40px rgba(139, 92, 246, 0.6), 0 0 60px rgba(59, 130, 246, 0.4);
  }
}

.animate-title-glow {
  animation: title-glow 3s ease-in-out infinite;
}

/* 3D文字效果 */
.text-3d {
  text-shadow:
    1px 1px 0px rgba(0,0,0,0.1),
    2px 2px 0px rgba(0,0,0,0.1),
    3px 3px 0px rgba(0,0,0,0.1),
    4px 4px 10px rgba(0,0,0,0.3);
}

/* 高级模糊效果 */
.blur-3xl {
  filter: blur(64px);
}

/* 标签动画效果 */
@keyframes badge-float {
  0%, 100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-5px) scale(1.02);
  }
}

.animate-badge-float {
  animation: badge-float 4s ease-in-out infinite;
}

/* 粒子闪烁效果 */
@keyframes particle-twinkle {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.animate-particle-twinkle {
  animation: particle-twinkle 2s ease-in-out infinite;
}

/* 渐变边框动画 */
@keyframes gradient-border {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient-border {
  background-size: 200% 200%;
  animation: gradient-border 3s ease infinite;
}

