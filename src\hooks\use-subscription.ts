'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'

interface UserInfo {
  userId: string
  email: string
  firstName?: string
  lastName?: string
  credits: number
  isAdmin: boolean
  hasMember: boolean
}

export function useSubscription() {
  const { data: session, status } = useSession()
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchUserInfo = async () => {
      if (status === 'loading') {
        return // 等待session加载完成
      }

      if (!session?.user) {
        setUserInfo(null)
        setIsLoading(false)
        return
      }

      try {
        setIsLoading(true)
        const response = await fetch('/api/user/me')
        const data = await response.json()

        if (data.success && data.user) {
          setUserInfo(data.user)
          setError(null)
        } else {
          setError(data.error || '获取用户信息失败')
          setUserInfo(null)
        }
      } catch (err) {
        console.error('获取用户信息失败:', err)
        setError('网络错误，请稍后重试')
        setUserInfo(null)
      } finally {
        setIsLoading(false)
      }
    }

    fetchUserInfo()
  }, [session, status])

  return {
    userInfo,
    isLoading,
    error,
    isSubscribed: userInfo?.hasMember || false,
    isLoggedIn: !!session?.user,
    refetch: () => {
      if (session?.user) {
        setIsLoading(true)
        setError(null)
        // 重新触发useEffect
        setUserInfo(null)
        // 强制重新获取数据
        const fetchUserInfo = async () => {
          try {
            const response = await fetch('/api/user/me', {
              cache: 'no-cache',
              headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
              }
            })
            const data = await response.json()

            if (data.success && data.user) {
              setUserInfo(data.user)
              setError(null)
            } else {
              setError(data.error || '获取用户信息失败')
              setUserInfo(null)
            }
          } catch (err) {
            console.error('获取用户信息失败:', err)
            setError('网络错误，请稍后重试')
            setUserInfo(null)
          } finally {
            setIsLoading(false)
          }
        }
        fetchUserInfo()
      }
    }
  }
}
