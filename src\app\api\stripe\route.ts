import { NextResponse } from 'next/server';
import Stripe from 'stripe';
import { sql } from '@/lib/postgres-client';
import { hasActiveAutoRenewalSubscription } from '@/lib/subscription-utils';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!,
    {
        stripeAccount: process.env.STRIPE_ACCOUNT_ID
    });

// 检查用户是否已有活跃的自动续费订阅
async function checkUserActiveSubscription(userId: string) {
    try {
        // 只检查是否有活跃的自动续费订阅（order_type = '1'）
        // 已取消续费但仍在有效期内的用户可以购买新订阅
        const hasActiveRenewal = await hasActiveAutoRenewalSubscription(userId);

        return {
            hasActiveSubscription: hasActiveRenewal
        };
    } catch (error) {
        console.error('检查用户订阅状态失败:', error);
        return { hasActiveSubscription: false };
    }
}

// 多语言错误消息
const getErrorMessage = (lang: string = 'zh') => {
    const messages = {
        zh: '您已有活跃的自动续费订阅，无法重复订阅。请先取消现有订阅或等待到期后再订阅。',
        en: 'You already have an active subscription plan and cannot subscribe again. Please cancel your existing subscription or wait for it to expire before subscribing again.',
        ja: '既にアクティブなサブスクリプションプランをお持ちのため、重複してサブスクリプションすることはできません。既存のサブスクリプションをキャンセルするか、期限切れになってから再度サブスクリプションしてください。',
        ko: '이미 활성 구독 플랜이 있어 중복 구독할 수 없습니다. 기존 구독을 취소하거나 만료될 때까지 기다린 후 다시 구독해주세요.',
        fr: 'Vous avez déjà un plan d\'abonnement actif et ne pouvez pas vous abonner à nouveau. Veuillez annuler votre abonnement existant ou attendre qu\'il expire avant de vous réabonner.',
        de: 'Sie haben bereits einen aktiven Abonnementplan und können sich nicht erneut anmelden. Bitte kündigen Sie Ihr bestehendes Abonnement oder warten Sie, bis es abläuft, bevor Sie sich erneut anmelden.',
        es: 'Ya tiene un plan de suscripción activo y no puede suscribirse nuevamente. Por favor cancele su suscripción existente o espere a que expire antes de suscribirse nuevamente.'
    };
    return messages[lang as keyof typeof messages] || messages.zh;
};

export async function POST(req: Request) {

    try {

        const { priceId, userId, type, customerEmail, lang } = await req.json();

        console.log('=== Stripe API 调试信息 ===');
        console.log('接收到的参数:', { priceId, userId, type, customerEmail, lang });

        if (!customerEmail) {
            console.error('customerEmail 参数为空');
            return NextResponse.json({ error: '用户邮箱不存在' }, { status: 400 });
        }

        // 如果是订阅模式，检查用户是否已有活跃订阅
        if (type === "2") { // 订阅模式
            const subscriptionCheck = await checkUserActiveSubscription(userId);
            if (subscriptionCheck.hasActiveSubscription) {
                console.log('用户已有活跃的自动续费订阅，拒绝创建新订阅');
                return NextResponse.json({
                    error: getErrorMessage(lang),
                    code: 'ACTIVE_SUBSCRIPTION_EXISTS'
                }, { status: 400 });
            }
        }
        
        const param: Stripe.Checkout.SessionCreateParams = {
            ui_mode: 'embedded',
            line_items: [
                {
                    price: priceId,
                    quantity: 1,
                },
            ],
            // redirect_on_completion: 'if_required',
            redirect_on_completion: 'never', 
            automatic_tax: {enabled: true},
            client_reference_id: userId,
            // return_url:`${req.headers.get("origin")}/return?session_id={CHECKOUT_SESSION_ID}`,
            metadata: {
                userId: userId,
                priceId: priceId
            },
            customer_email: customerEmail,
            custom_text: {
                submit: {
                    message: '邮箱已自动填充并锁定，以确保支付与您的账户关联',
                },
            },
        }

        if(type === "1"){
            param.mode = 'payment';
            param.payment_intent_data = { metadata: { userId: userId, priceId: priceId } };
        }else{
            param.mode = 'subscription';
            param.subscription_data = { metadata: { userId: userId, priceId: priceId } };
        }
        
        console.log('创建 Stripe 会话参数:', {
            customer_email: param.customer_email,
            mode: param.mode,
            userId: userId
        });
        
        const session = await stripe.checkout.sessions.create(param);
        
        console.log('Stripe 会话创建成功:', session.id);

        return NextResponse.json({ clientSecret: session.client_secret });
    } catch (error) {
        console.log("payment error", error)
        return NextResponse.json({ error: 'payment error' }, { status: 500 });
    }
}