'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { checkUserSubscriptionStatus } from '@/components/auth/login-redirect-handler'

/**
 * 用于检查用户订阅状态的Hook
 */
export function useSubscriptionStatus() {
  const { data: session, status } = useSession()
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    checkSubscription()
  }, [session, status])

  const checkSubscription = async () => {
    setIsLoading(true)
    setError(null)

    // 如果还在加载认证状态，等待
    if (status === 'loading') {
      return
    }

    // 如果未登录，设置为未订阅
    if (status === 'unauthenticated' || !session?.user) {
      setIsSubscribed(false)
      setIsLoading(false)
      return
    }

    try {
      const { isSubscribed: userIsSubscribed, error: checkError } = await checkUserSubscriptionStatus()
      
      if (checkError) {
        setError(checkError)
        setIsSubscribed(false)
      } else {
        setIsSubscribed(userIsSubscribed)
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '检查订阅状态失败'
      setError(errorMessage)
      setIsSubscribed(false)
    } finally {
      setIsLoading(false)
    }
  }

  const refetch = () => {
    checkSubscription()
  }

  return {
    isSubscribed,
    isLoading,
    error,
    isLoggedIn: status === 'authenticated' && !!session?.user,
    refetch
  }
}

/**
 * 用于检查是否应该显示订阅相关功能的Hook
 */
export function useSubscriptionFeatures() {
  const { isSubscribed, isLoading, isLoggedIn } = useSubscriptionStatus()

  return {
    // 是否应该显示完善信息对话框
    shouldShowProfileDialog: isLoggedIn && isSubscribed,
    // 是否应该显示订阅提示
    shouldShowSubscriptionPrompt: isLoggedIn && !isSubscribed,
    // 是否可以访问高级功能
    canAccessPremiumFeatures: isSubscribed,
    // 是否正在加载
    isLoading,
    // 是否已登录
    isLoggedIn,
    // 是否已订阅
    isSubscribed
  }
}
