import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/[...nextauth]/route'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // 这里我们不需要在数据库中做任何操作
    // 因为isNewUser标识只存在于JWT token中
    // 当用户下次刷新token时，isNewUser将不会被设置
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Clear new user flag error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
