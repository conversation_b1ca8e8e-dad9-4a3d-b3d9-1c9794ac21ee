'use client'

import { useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { checkUserSubscriptionStatus } from '@/components/auth/login-redirect-handler'

interface UseLoginRedirectOptions {
  lang: string
  enabled?: boolean
  onRedirect?: (destination: string) => void
}

/**
 * 登录后重定向的Hook
 * 检查用户订阅状态并自动重定向
 */
export function useLoginRedirect({ lang, enabled = true, onRedirect }: UseLoginRedirectOptions) {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (!enabled || status === 'loading') return

    if (status === 'unauthenticated') {
      const destination = `/${lang}`
      if (onRedirect) onRedirect(destination)
      router.replace(destination)
      return
    }

    if (session?.user) {
      handleRedirect()
    }
  }, [session, status, enabled, lang])

  async function handleRedirect() {
    try {
      const { isSubscribed, error } = await checkUserSubscriptionStatus()
      
      if (error) {
        console.error('检查订阅状态失败:', error)
        const destination = `/${lang}`
        if (onRedirect) onRedirect(destination)
        router.replace(destination)
        return
      }

      const destination = isSubscribed ? `/${lang}` : `/${lang}/pricing`
      if (onRedirect) onRedirect(destination)
      router.replace(destination)
    } catch (error) {
      console.error('重定向处理失败:', error)
      const destination = `/${lang}`
      if (onRedirect) onRedirect(destination)
      router.replace(destination)
    }
  }

  return {
    isLoading: status === 'loading',
    isAuthenticated: status === 'authenticated',
    session
  }
}

/**
 * 检查当前用户是否需要重定向到价格页面
 */
export async function shouldRedirectToPricing(): Promise<boolean> {
  try {
    const { isSubscribed, error } = await checkUserSubscriptionStatus()
    
    if (error) {
      console.error('检查订阅状态失败:', error)
      return false
    }

    return !isSubscribed
  } catch (error) {
    console.error('检查是否需要重定向失败:', error)
    return false
  }
}
