'use client'

import { useEffect, useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter, usePathname } from 'next/navigation'
import { checkUserSubscriptionStatus } from './login-redirect-handler'

interface SubscriptionGuardProps {
  lang: string
  children: React.ReactNode
  requireSubscription?: boolean
  redirectTo?: string
}

/**
 * 订阅状态守卫组件
 * 检查用户订阅状态，如果需要订阅但用户未订阅，则重定向到价格页面
 */
export function SubscriptionGuard({ 
  lang, 
  children, 
  requireSubscription = false,
  redirectTo 
}: SubscriptionGuardProps) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const pathname = usePathname()
  const [isChecking, setIsChecking] = useState(true)
  const [hasAccess, setHasAccess] = useState(false)

  useEffect(() => {
    checkAccess()
  }, [session, status, pathname])

  async function checkAccess() {
    setIsChecking(true)

    // 如果还在加载认证状态，等待
    if (status === 'loading') {
      return
    }

    // 如果未登录，允许访问（由其他组件处理登录逻辑）
    if (status === 'unauthenticated') {
      setHasAccess(true)
      setIsChecking(false)
      return
    }

    // 如果不需要订阅，直接允许访问
    if (!requireSubscription) {
      setHasAccess(true)
      setIsChecking(false)
      return
    }

    // 如果已登录且需要检查订阅状态
    if (session?.user) {
      try {
        const { isSubscribed, error } = await checkUserSubscriptionStatus()
        
        if (error) {
          console.error('检查订阅状态失败:', error)
          setHasAccess(true) // 出错时允许访问
          setIsChecking(false)
          return
        }

        if (!isSubscribed) {
          // 用户未订阅，重定向到价格页面或指定页面
          const destination = redirectTo || `/${lang}/pricing`
          router.replace(destination)
          setHasAccess(false)
        } else {
          setHasAccess(true)
        }
      } catch (error) {
        console.error('订阅状态检查失败:', error)
        setHasAccess(true) // 出错时允许访问
      }
    }

    setIsChecking(false)
  }

  // 如果正在检查或没有访问权限，显示加载状态
  if (isChecking || !hasAccess) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-slate-600">
            {lang === 'zh' ? '检查访问权限...' : 'Checking access...'}
          </p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}

/**
 * 高阶组件：为页面添加订阅检查
 */
export function withSubscriptionGuard<P extends object>(
  Component: React.ComponentType<P>,
  options: {
    requireSubscription?: boolean
    redirectTo?: string
  } = {}
) {
  return function WrappedComponent(props: P & { lang: string }) {
    return (
      <SubscriptionGuard 
        lang={props.lang}
        requireSubscription={options.requireSubscription}
        redirectTo={options.redirectTo}
      >
        <Component {...props} />
      </SubscriptionGuard>
    )
  }
}

/**
 * 用于检查特定页面是否需要订阅的工具函数
 */
export function getPageSubscriptionRequirement(pathname: string): {
  requireSubscription: boolean
  redirectTo?: string
} {
  // 定义需要订阅的页面路径
  const subscriptionRequiredPaths = [
    '/diet-plan',
    '/profile',
    // 可以添加更多需要订阅的页面
  ]

  // 检查当前路径是否需要订阅
  const requireSubscription = subscriptionRequiredPaths.some(path => 
    pathname.includes(path)
  )

  return {
    requireSubscription,
    redirectTo: requireSubscription ? undefined : undefined // 使用默认重定向
  }
}
