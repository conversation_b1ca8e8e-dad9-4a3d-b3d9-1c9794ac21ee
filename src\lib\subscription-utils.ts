import { sql } from '@/lib/postgres-client';

export interface SubscriptionRecord {
  order_date: string;
  credit_amount: number;
  order_type: string;
  subscription_id?: string;
}

export interface CumulativeSubscriptionResult {
  isActive: boolean;
  totalExpiryDate: Date;
  subscriptionHistory: Array<{
    orderDate: Date;
    creditAmount: number;
    periodDays: number;
    isActive: boolean;
    subscriptionId?: string;
  }>;
}

/**
 * 计算用户的累积订阅时间
 * @param userId 用户ID
 * @returns 累积订阅结果
 */
export async function calculateCumulativeSubscription(userId: string): Promise<CumulativeSubscriptionResult> {
  try {
    // 获取用户所有订阅记录，按时间顺序排序
    const { rows } = await sql`
      SELECT
        order_date,
        credit_amount,
        order_type,
        subscription_id
      FROM nf_subscription
      WHERE user_id = ${userId}
      ORDER BY order_date ASC
    `;

    if (rows.length === 0) {
      return {
        isActive: false,
        totalExpiryDate: new Date(),
        subscriptionHistory: []
      };
    }

    const today = new Date();
    let cumulativeExpiryDate = new Date(0); // 从最早时间开始
    const subscriptionHistory: CumulativeSubscriptionResult['subscriptionHistory'] = [];

    // 遍历所有订阅记录，累积计算时间
    for (const subscription of rows) {
      const orderDate = new Date(subscription.order_date);
      const creditAmount = Number(subscription.credit_amount);
      const isActive = subscription.order_type === '1';

      // 根据credit_amount判断订阅周期
      let periodDays: number;
      switch (creditAmount) {
        case 1: periodDays = 30; break;   // 月度
        case 2: periodDays = 90; break;   // 季度
        case 3: periodDays = 365; break;  // 年度
        default: periodDays = 30;
      }

      // 如果这是第一个订阅，或者新订阅的开始时间在当前累积到期时间之后
      // 则从订阅开始时间计算；否则从累积到期时间开始计算
      let startDate: Date;
      if (cumulativeExpiryDate.getTime() === 0 || orderDate > cumulativeExpiryDate) {
        startDate = orderDate;
      } else {
        startDate = cumulativeExpiryDate;
      }

      // 计算这个订阅的到期时间
      const thisSubscriptionExpiry = new Date(startDate);
      thisSubscriptionExpiry.setDate(startDate.getDate() + periodDays);

      // 更新累积到期时间
      if (thisSubscriptionExpiry > cumulativeExpiryDate) {
        cumulativeExpiryDate = thisSubscriptionExpiry;
      }

      subscriptionHistory.push({
        orderDate,
        creditAmount,
        periodDays,
        isActive,
        subscriptionId: subscription.subscription_id
      });
    }

    // 检查累积的订阅是否仍然有效
    const isActive = today <= cumulativeExpiryDate;

    return {
      isActive,
      totalExpiryDate: cumulativeExpiryDate,
      subscriptionHistory
    };

  } catch (error) {
    console.error('计算累积订阅时间失败:', error);
    return {
      isActive: false,
      totalExpiryDate: new Date(),
      subscriptionHistory: []
    };
  }
}

/**
 * 检查用户是否有活跃的自动续费订阅（用于重复订阅检查）
 * @param userId 用户ID
 * @returns 是否有活跃的自动续费订阅
 */
export async function hasActiveAutoRenewalSubscription(userId: string): Promise<boolean> {
  try {
    // 只检查最新的订阅记录是否为活跃状态（order_type = '1'）
    const { rows } = await sql`
      SELECT order_type
      FROM nf_subscription
      WHERE user_id = ${userId}
      ORDER BY order_date DESC
      LIMIT 1
    `;

    if (rows.length === 0) {
      return false;
    }

    // 只有 order_type = '1' 才算活跃的自动续费订阅
    return rows[0].order_type === '1';
  } catch (error) {
    console.error('检查活跃自动续费订阅失败:', error);
    return false;
  }
}

/**
 * 获取用户订阅的详细信息（用于显示）
 * @param userId 用户ID
 */
export async function getUserSubscriptionDetails(userId: string) {
  const result = await calculateCumulativeSubscription(userId);

  if (result.subscriptionHistory.length === 0) {
    return null;
  }

  // 获取最新的订阅记录用于显示订阅类型
  const latestSubscription = result.subscriptionHistory[result.subscriptionHistory.length - 1];

  let subscriptionType: string;
  switch (latestSubscription.creditAmount) {
    case 1: subscriptionType = 'monthly'; break;
    case 2: subscriptionType = 'quarterly'; break;
    case 3: subscriptionType = 'annual'; break;
    default: subscriptionType = 'monthly';
  }

  return {
    isActive: result.isActive,
    subscriptionType,
    totalExpiryDate: result.totalExpiryDate,
    subscriptionCount: result.subscriptionHistory.length,
    totalDays: Math.ceil((result.totalExpiryDate.getTime() - result.subscriptionHistory[0].orderDate.getTime()) / (1000 * 60 * 60 * 24))
  };
}
