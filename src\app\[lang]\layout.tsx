import type React from "react"
import '@/styles/globals.css'
import { ThemeProvider } from "@/components/theme-provider"
import { type Locale } from "@/i18n-config";
import { Geist } from 'next/font/google'
import { Toaster } from "@/components/ui/toaster"
import type { Metadata } from "next"
import { GoogleAnalytics } from '@next/third-parties/google'
import { NextAuthProvider } from "@/components/auth/next-auth-provider";
import { GlobalAuthHandler } from "@/components/auth/global-auth-handler";

export const metadata: Metadata = {
  title: "瘦身健康评估 - 智能血压风险分析系统",
  description:
    "通过详细的健康问卷，使用AI技术进行瘦身风险评估，获取个性化的血压控制方案、饮食建议、运动指导和生活习惯调整建议。",
  icons: {
    icon: [
      {
        url: '/health-icon.svg',
        type: 'image/svg+xml',
      },
      {
        url: '/favicon.svg',
        type: 'image/svg+xml',
      }
    ],
    shortcut: '/favicon.svg',
    apple: '/health-icon.svg',
  },
}

const geist = Geist({
  subsets: ['latin'],
})

export default async function RootLayout({
  children,
  params
}: Readonly<{
  children: React.ReactNode,
  params: Promise<{ lang: Locale }>
}>) {
  const { lang } = await params

  return (
    <html lang={lang} className={geist.className} suppressHydrationWarning>
      <body>
        <NextAuthProvider>
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem={false} disableTransitionOnChange>
          <Toaster />
          <GlobalAuthHandler lang={lang} />
            {children}
        </ThemeProvider>
        </NextAuthProvider>
        <GoogleAnalytics gaId="G-16ZH7FHTTQ" />
      </body>
    </html>
  )
}

