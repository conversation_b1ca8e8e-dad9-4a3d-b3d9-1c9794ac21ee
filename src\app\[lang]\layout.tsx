import type React from "react"
import '@/styles/globals.css'
import { ThemeProvider } from "@/components/theme-provider"
import { type Locale } from "@/i18n-config";
import { Geist } from 'next/font/google'
import { Toaster } from "@/components/ui/toaster"
import type { Metadata } from "next"
import { GoogleAnalytics } from '@next/third-parties/google'
import { NextAuthProvider } from "@/components/auth/next-auth-provider";
import { GlobalAuthHandler } from "@/components/auth/global-auth-handler";

export const metadata: Metadata = {
  title: "多语言翻译服务 - 智能翻译系统",
  description:
    "通过输入语言文字或语言等，获取多语言翻译结果。",
  icons: {
    icon: [
      {
        url: '/health-icon.svg',
        type: 'image/svg+xml',
      },
      {
        url: '/favicon.svg',
        type: 'image/svg+xml',
      }
    ],
    shortcut: '/favicon.svg',
    apple: '/health-icon.svg',
  },
}

const geist = Geist({
  subsets: ['latin'],
})

export default async function RootLayout({
  children,
  params
}: Readonly<{
  children: React.ReactNode,
  params: Promise<{ lang: Locale }>
}>) {
  const { lang } = await params

  return (
    <html lang={lang} className={geist.className} suppressHydrationWarning>
      <body>
        <NextAuthProvider>
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem={false} disableTransitionOnChange>
          <Toaster />
          <GlobalAuthHandler lang={lang} />
            {children}
        </ThemeProvider>
        </NextAuthProvider>
        <GoogleAnalytics gaId="G-16ZH7FHTTQ" />
      </body>
    </html>
  )
}

