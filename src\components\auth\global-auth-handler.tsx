'use client'

import { useEffect, useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter, usePathname } from 'next/navigation'
import { checkUserSubscriptionStatus } from './login-redirect-handler'

interface GlobalAuthHandlerProps {
  lang: string
}

/**
 * 全局认证处理组件
 * 在用户登录后检查订阅状态，如果未订阅且不在价格页面，则重定向到价格页面
 */
export function GlobalAuthHandler({ lang }: GlobalAuthHandlerProps) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const pathname = usePathname()
  const [hasChecked, setHasChecked] = useState(false)

  useEffect(() => {
    // 重置检查状态当session变化时
    setHasChecked(false)
  }, [session?.user?.id])

  useEffect(() => {
    if (status === 'loading' || hasChecked) return

    // 只在用户已登录时检查
    if (status === 'authenticated' && session?.user) {
      checkAndRedirect()
    } else {
      setHasChecked(true)
    }
  }, [session, status, pathname, hasChecked])

  async function checkAndRedirect() {
    try {
      // 如果已经在价格页面、认证页面或其他特殊页面，不进行重定向
      const skipPaths = [
        '/pricing',
        '/auth',
        '/callback',
        '/legal',
        '/about',
        '/contact'
      ]

      const shouldSkip = skipPaths.some(path => pathname.includes(path))
      if (shouldSkip) {
        setHasChecked(true)
        return
      }

      // 检查用户订阅状态
      const { isSubscribed, error } = await checkUserSubscriptionStatus()
      
      if (error) {
        console.error('检查订阅状态失败:', error)
        setHasChecked(true)
        return
      }

      // 如果用户未订阅，重定向到价格页面
      if (!isSubscribed) {
        console.log('用户未订阅，重定向到价格页面')
        router.replace(`/${lang}/pricing`)
      }

      setHasChecked(true)
    } catch (error) {
      console.error('全局认证检查失败:', error)
      setHasChecked(true)
    }
  }

  // 这个组件不渲染任何内容
  return null
}

/**
 * 检查是否应该跳过重定向的工具函数
 */
export function shouldSkipRedirect(pathname: string): boolean {
  const skipPaths = [
    '/pricing',
    '/auth',
    '/callback',
    '/legal',
    '/about',
    '/contact',
    '/api'
  ]

  return skipPaths.some(path => pathname.includes(path))
}

/**
 * 用于手动触发订阅状态检查的函数
 */
export async function triggerSubscriptionCheck(lang: string): Promise<boolean> {
  try {
    const { isSubscribed, error } = await checkUserSubscriptionStatus()
    
    if (error) {
      console.error('检查订阅状态失败:', error)
      return false
    }

    if (!isSubscribed) {
      window.location.href = `/${lang}/pricing`
      return false
    }

    return true
  } catch (error) {
    console.error('订阅状态检查失败:', error)
    return false
  }
}
