'use server'
import { getCurrentUser } from '@/lib/auth';
import { sql } from '@/lib/postgres-client';
import { UserOrder, UserSubscription } from '@/types/order';
import { cancelSubscriptionById } from "@/actions/stripe-billing";
import { updateSubscription } from "@/actions/credits";
import { calculateCumulativeSubscription } from '@/lib/subscription-utils';

export async function findOrderByUserId(): Promise<UserOrder[]> {
    try {
        const user = await getCurrentUser();
        if (!user) {
            return [];
        }

        const { rows } = await sql`
            SELECT 
                order_number,
                credit_amount,
                order_price,
                order_date,
                credit_type,
                created_at
            FROM nf_credits where credit_type in ('1','2','3') and user_id = ${user.userId}`;

        return rows.map(row => ({
            orderId: row.order_number,
            credits: row.credit_amount,
            price: row.order_price,
            date: row.order_date || row.created_at,
            type: row.credit_type === '1' ? 'subscription' :
                row.credit_type === '2' ? 'one-time' : '',
            status: row.credit_type === '1' || row.credit_type === '2' ? 'completed' :
                row.credit_type === '3' ? 'refunded' : '',
        }));

    } catch (error) {
        console.error('Failed to get user orders:', error);
        return [];
    }
}

export async function findSubscriptionByUserId(): Promise<UserSubscription[]> {
    try {
        const user = await getCurrentUser();
        if (!user) {
            return [];
        }

        // 获取累积订阅信息
        const cumulativeResult = await calculateCumulativeSubscription(user.userId);

        if (cumulativeResult.subscriptionHistory.length === 0) {
            return [];
        }

        // 获取最新的订阅记录用于显示
        const { rows } = await sql`
        SELECT
            subscription_id,
            credit_amount,
            order_price,
            order_date,
            order_type
        FROM nf_subscription
        WHERE user_id = ${user.userId}
        ORDER BY order_date DESC
        LIMIT 1`;

        if (rows.length === 0) {
            return [];
        }

        const latestRow = rows[0];
        const isActive = latestRow.order_type === '1';

        // 计算累积的订阅类型（基于最新订阅）
        const latestCreditAmount = Number(latestRow.credit_amount);
        let subscriptionType: string;
        switch (latestCreditAmount) {
            case 1: subscriptionType = 'monthly'; break;
            case 2: subscriptionType = 'quarterly'; break;
            case 3: subscriptionType = 'annual'; break;
            default: subscriptionType = 'monthly';
        }

        // 计算累积的总价格
        const totalPrice = await sql`
        SELECT SUM(CAST(order_price AS DECIMAL)) as total_price
        FROM nf_subscription
        WHERE user_id = ${user.userId}`;

        const cumulativeTotalPrice = totalPrice.rows[0]?.total_price || latestRow.order_price;

        // 格式化价格显示
        const formattedPrice = cumulativeResult.subscriptionHistory.length > 1 ?
            `$${Number(cumulativeTotalPrice).toFixed(2)}` :
            latestRow.order_price;

        // 返回一个汇总的订阅记录，显示累积信息
        return [{
            orderId: latestRow.subscription_id,
            credits: latestRow.credit_amount,
            subscriptionType: subscriptionType,
            price: formattedPrice, // 显示累积总价或单个订阅价格
            date: latestRow.order_date,
            renewalDate: cumulativeResult.totalExpiryDate.toISOString(),
            isActive: isActive,
            status: isActive ? 'active' : 'cancelled',
            // 累积订阅信息
            isCumulative: true,
            totalSubscriptions: cumulativeResult.subscriptionHistory.length,
            cumulativeExpiryDate: cumulativeResult.totalExpiryDate.toISOString()
        }];

    } catch (error) {
        console.error('Failed to get user subscriptions:', error);
        return [];
    }
}

export async function updateSubscriptionByOrderId(subscriptionId: string) {
    try {
        // 尝试在Stripe中取消订阅
        const result = await cancelSubscriptionById(subscriptionId);

        // 如果Stripe取消成功，更新本地数据库
        if (result) {
            await updateSubscription(subscriptionId);
        }

        return result;
    } catch (error: any) {
        console.error('Stripe取消订阅失败:', error);

        // 如果Stripe中订阅不存在（404错误），直接更新本地数据库
        if (error.code === 'resource_missing' || error.statusCode === 404) {
            console.log('Stripe中订阅不存在，直接更新本地状态');
            const localResult = await updateSubscription(subscriptionId);
            return localResult > 0; // 返回本地更新是否成功
        }

        // 其他错误重新抛出
        throw error;
    }
}

export async function getSubscriptionByUserId(userId: string) {
    try {
        const { rows } = await sql`
            SELECT 
                subscription_id,
                order_type
            FROM nf_subscription 
            WHERE user_id = ${userId} 
            ORDER BY order_date DESC 
            LIMIT 1`;

        if (rows.length > 0) {
            return rows[0];
        }
        return null;
    } catch (error) {
        console.error('Failed to get user subscription by userId:', error);
        return null;
    }
}

// 兼容性函数（将会被弃用）
export async function getSubscriptionByClerkId(clerkId: string) {
    return getSubscriptionByUserId(clerkId);
}
