'use client'

import { useSession } from 'next-auth/react'
import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Loader2 } from 'lucide-react'
import { useLoginRedirect } from '@/hooks/use-login-redirect'

interface CallbackPageProps {
  params: {
    lang: string
  }
}

export default function CallbackPage({ params }: CallbackPageProps) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const { lang } = params

  // 对于现有用户，使用hook处理重定向
  const shouldUseHook = session?.user && !(session.user as any).isNewUser
  useLoginRedirect({
    lang,
    enabled: shouldUseHook,
    onRedirect: (destination) => {
      console.log('重定向到:', destination)
    }
  })

  useEffect(() => {
    if (status === 'loading') return // 还在加载中

    if (status === 'unauthenticated') {
      // 未认证，跳转到首页
      router.replace(`/${lang}`)
      return
    }

    if (session?.user) {
      // 检查是否是新用户
      const isNewUser = (session.user as any).isNewUser

      if (isNewUser) {
        // 清除新用户标识
        fetch('/api/auth/clear-new-user', { method: 'POST' })
          .catch(console.error)

        // 新用户跳转到价格页面
        router.replace(`/${lang}/pricing`)
      }
      // 现有用户的重定向逻辑由useLoginRedirect hook处理
    }
  }, [session, status, router, lang])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50/30 to-emerald-50/20">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
        <p className="text-lg text-slate-600">
          {lang === 'zh' ? '正在跳转...' : 'Redirecting...'}
        </p>
      </div>
    </div>
  )
}
