'use client'

import { useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'

interface LoginRedirectHandlerProps {
  lang: string
  onRedirect?: () => void
}

/**
 * 登录后重定向处理组件
 * 检查用户订阅状态并决定跳转到价格页面还是首页
 */
export function LoginRedirectHandler({ lang, onRedirect }: LoginRedirectHandlerProps) {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === 'loading') return // 还在加载中

    if (status === 'unauthenticated') {
      // 未认证，跳转到首页
      router.replace(`/${lang}`)
      return
    }

    if (session?.user) {
      checkSubscriptionAndRedirect()
    }
  }, [session, status, router, lang])

  async function checkSubscriptionAndRedirect() {
    try {
      const userResponse = await fetch('/api/user/me', {
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })
      const userData = await userResponse.json()
      
      if (userData.success && userData.user) {
        // 如果用户没有订阅，跳转到价格页面
        if (!userData.user.hasMember) {
          if (onRedirect) onRedirect()
          router.replace(`/${lang}/pricing`)
        } else {
          // 有订阅的用户跳转到首页
          if (onRedirect) onRedirect()
          router.replace(`/${lang}`)
        }
      } else {
        // 获取用户信息失败，默认跳转到首页
        if (onRedirect) onRedirect()
        router.replace(`/${lang}`)
      }
    } catch (error) {
      console.error('检查用户订阅状态失败:', error)
      // 出错时默认跳转到首页
      if (onRedirect) onRedirect()
      router.replace(`/${lang}`)
    }
  }

  return null // 这个组件不渲染任何内容
}

/**
 * 检查用户订阅状态的工具函数
 * 可以在其他地方使用
 */
export async function checkUserSubscriptionStatus(): Promise<{
  isSubscribed: boolean
  userInfo: any
  error?: string
}> {
  try {
    const userResponse = await fetch('/api/user/me', {
      cache: 'no-cache',
      headers: {
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    })
    const userData = await userResponse.json()
    
    if (userData.success && userData.user) {
      return {
        isSubscribed: userData.user.hasMember,
        userInfo: userData.user
      }
    } else {
      return {
        isSubscribed: false,
        userInfo: null,
        error: userData.error || '获取用户信息失败'
      }
    }
  } catch (error) {
    console.error('检查用户订阅状态失败:', error)
    return {
      isSubscribed: false,
      userInfo: null,
      error: '网络错误，请稍后重试'
    }
  }
}

/**
 * 登录后重定向的工具函数
 * 可以在登录成功后直接调用
 */
export async function redirectAfterLogin(lang: string): Promise<void> {
  const { isSubscribed, error } = await checkUserSubscriptionStatus()
  
  if (error) {
    console.error('检查订阅状态失败:', error)
    // 出错时默认跳转到首页
    window.location.href = `/${lang}`
    return
  }

  if (!isSubscribed) {
    // 没有订阅，跳转到价格页面
    window.location.href = `/${lang}/pricing`
  } else {
    // 有订阅，跳转到首页
    window.location.href = `/${lang}`
  }
}
