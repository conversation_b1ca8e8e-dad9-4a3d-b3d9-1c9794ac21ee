{"meta": {"title": "Evaluación de salud de hipertensión - Menú dietético de hipertensión | Guía de ejercicios para hipertensión & Plan de dieta para presión arterial", "description": "A través de cuestionarios de salud detallados, use tecnología de IA para la evaluación de riesgo de hipertensión para obtener planes personalizados de control de presión arterial, recomendaciones dietéticas, orientación de ejercicios y sugerencias de ajuste de estilo de vida.", "alt": "Evaluación de salud de hipertensión - Sistema inteligente de análisis de riesgo de presión arterial", "keywords": "dieta DASH,alimentos para hipertensión,dieta saludable para hipertensión"}, "hero": {"aiLabel": "Evaluación Adelgazamiento con IA", "title": "Evaluación Adelgazamiento Saludable", "subtitle": "Profesional · Inteligente · Personalizado", "description": "Basado en estándares nutricionales internacionales y análisis IA\nPlan de control de peso personalizado para su salud corporal", "ctaPrimary": "Iniciar evaluación adelgazamiento", "ctaSecondary": "Detalles", "features": [{"title": "Evaluación grasa corporal", "description": "Evaluación IMC y porcentaje grasa según estándares científicos"}, {"title": "Plan personalizado", "description": "Estrategias adelgazamiento saludable adaptadas"}, {"title": "Monitoreo continuo", "description": "Seguimiento peso prolongado con ajustes dinámicos"}]}, "pyramid": {"title": "Pirámide de gestión de presión arterial", "subtitle": "Estructura científica de niveles para protección cardiovascular integral", "managementLevels": "Niveles de gestión", "fromAcuteToPrevention": "De agudo a prevención", "healthScore": "85 puntos", "healthScoreLabel": "Puntuación de salud", "levels": [{"level": 1, "label": "Fase aguda", "percentage": "80%"}, {"level": 2, "label": "Medicación", "percentage": "60%"}, {"level": 3, "label": "Estilo de vida", "percentage": "40%"}, {"level": 4, "label": "Monitoreo", "percentage": "20%"}, {"level": 5, "label": "Prevención", "percentage": "5%"}], "pyramidLevels": [{"title": "<PERSON><PERSON><PERSON>", "subtitle": "Control urgente y evaluación de riesgo", "level": "Nivel 1", "category": "Intervención crítica", "systolic": "≥180", "diastolic": "≥110", "intervention": "Inmediata", "systolicUnit": "Presión sistólica mmHg", "diastolicUnit": "Presión diastólica mmHg", "interventionUnit": "Intervención médica"}, {"title": "Tratamiento farmacológico", "subtitle": "Protocolo estandarizado de antihipertensivos", "level": "Nivel 2", "category": "Intervención médica", "systolic": "160-179", "diastolic": "100-109", "intervention": "4-6 semanas", "systolicUnit": "Presión sistólica mmHg", "diastolicUnit": "Presión diastólica mmHg", "interventionUnit": "Periodo de efecto"}, {"title": "Intervención de hábitos", "subtitle": "Ajustes dietéticos y conductuales", "level": "Nivel 3", "category": "Mejora conductual", "systolic": "140-159", "diastolic": "90-99", "intervention": "3-6 meses", "systolicUnit": "Presión sistólica mmHg", "diastolicUnit": "Presión diastólica mmHg", "interventionUnit": "Periodo de mejora"}, {"title": "Monitoreo de salud", "subtitle": "Chequeos regulares y seguimiento", "level": "Nivel 4", "category": "Prevención y monitoreo", "systolic": "130-139", "diastolic": "85-89", "intervention": "<PERSON><PERSON><PERSON>", "systolicUnit": "Presión sistólica mmHg", "diastolicUnit": "Presión diastólica mmHg", "interventionUnit": "Frecuencia de monitoreo"}, {"title": "Prevención de salud", "subtitle": "Educación y prevención de riesgos", "level": "Nivel 5", "category": "Prevención básica", "systolic": "<130", "diastolic": "<85", "intervention": "<PERSON><PERSON>", "systolicUnit": "Presión sistólica mmHg", "diastolicUnit": "Presión diastólica mmHg", "interventionUnit": "Frecuencia de chequeos"}], "description": "Nuestro sistema IA elaborará un plan científico multinivel basado en su presión arterial y estado de salud", "stats": [{"number": "5 niveles", "label": "Niveles de gestión"}, {"number": "360°", "label": "Cobertura completa"}, {"number": "Personalizado", "label": "Plan adaptado"}, {"number": "IA inteligente", "label": "<PERSON><PERSON><PERSON><PERSON>í<PERSON>"}]}, "statistics": {"stats": [{"number": "50k+", "label": "Usuarios"}, {"number": "98%", "label": "Precisión"}, {"number": "30 días", "label": "Plan personalizado"}, {"number": "Profesional", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, "assessment": {"title": "Comience su evaluación", "subtitle": "Obtenga en minutos un informe profesional de gestión de peso y plan de pérdida de peso saludable personalizado"}, "cases": {"title": "Casos de Éxito", "subtitle": "Historias reales de pérdida de peso saludable, soluciones científicas que ayudan a lograr objetivos de salud", "examples": [{"icon": "👩‍💼", "title": "<PERSON>ici<PERSON><PERSON>", "description": "Trabajo sedentario, 15kg de sobrepeso, perdió peso exitosamente y mantuvo forma corporal saludable con planes personalizados", "result": "-12kg", "resultLabel": "Pérdida en 3 meses"}, {"icon": "👨‍🍳", "title": "Chef <PERSON>", "description": "Hábitos alimentarios irregulares por profesión, alto porcentaje de grasa corporal, mejoró con nutrición científica", "result": "-8%", "resultLabel": "Reducción de grasa"}, {"icon": "👩‍🎓", "title": "Estudi<PERSON>", "description": "Alta presión de estudios y horarios irregulares causando fluctuaciones de peso, recuperó equilibrio corporal", "result": "95%", "resultLabel": "Indicadores mejorados"}], "disclaimer": "* Los casos anteriores son comentarios reales de usuarios. Los resultados específicos pueden variar según el individuo."}, "process": {"title": "Proceso de evaluación inteligente", "subtitle": "4 pasos simples para evaluación médica profesional de pérdida de peso", "steps": [{"title": "Datos básicos", "desc": "Complete información básica (edad, sexo, altura, peso)", "step": "01"}, {"title": "Datos de peso", "desc": "Registre sus mediciones actuales y recientes de peso", "step": "02"}, {"title": "Historial médico", "desc": "Detalle antecedentes y tratamientos", "step": "03"}, {"title": "Análisis IA", "desc": "Generación de evaluación de pérdida de peso y consejos personalizados", "step": "04"}]}, "advantages": {"title": "Por qué elegirnos", "subtitle": "Integrando tecnología IA de vanguardia con estándares nutricionales internacionales para proporcionar soluciones de pérdida de peso personalizadas, científicas, seguras y efectivas", "features": [{"title": "Análisis inteligente de IA", "subtitle": "Motor inteligente de gestión de peso basado en ciencia nutricional", "description": "Utilizando algoritmos avanzados de gestión de peso internacionales, integrando los últimos hallazgos de investigación nutricional, la IA analiza inteligentemente sus datos corporales, hábitos alimentarios y estilo de vida para proporcionar evaluación precisa de grasa corporal y recomendaciones de pérdida de peso.", "tags": ["<PERSON><PERSON><PERSON><PERSON> grasa corporal", "Evaluación nutricional", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Recomendaciones inteligentes"]}, {"title": "Plan de pérdida de peso personalizado", "subtitle": "Estrategias científicas de reducción de peso a medida", "description": "Basado en sus características corporales, peso objetivo, preferencias dietéticas y capacidad de ejercicio, genera inteligentemente planes exclusivos de pérdida de peso, incluyendo planificación de comidas personalizada, programas de ejercicio, optimización del sueño y orientación integral.", "tags": ["Dieta personalizada", "Plan de ejercicio", "Optimización sueño", "Seguimiento progreso"]}], "highlights": [{"title": "Científico y seguro", "description": "Basado en estándares nutricionales internacionales y datos de investigación clínica, garantizando un proceso saludable de pérdida de peso y evitando la reducción extrema de peso"}, {"title": "Rápido y conveniente", "description": "Complete la evaluación de peso en solo 3-5 minutos, reciba instantáneamente un plan personalizado de pérdida de peso y orientación detallada de ejecución"}, {"title": "Equipo profesional", "description": "Creado por nutricionistas senior, expertos en fitness y equipo de tecnología IA, optimizando continuamente los algoritmos de pérdida de peso"}]}, "faq": {"title": "Preguntas frecuentes", "subtitle": "Respuestas a dudas comunes sobre evaluación de pérdida de peso saludable", "items": [{"question": "¿Qué tan precisa es esta evaluación?", "answer": "Nuestra evaluación se basa en estándares nutricionales internacionales y guías de manejo de peso combinadas con análisis IA, ofreciendo alto valor indicativo. Con precisión superior al 98%, no reemplaza diagnóstico de nutricionista profesional pero es referencia útil para discutir con su nutricionista o doctor."}, {"question": "¿Mis datos personales están seguros?", "answer": "Protegemos estrictamente su privacidad. Todos los datos de salud se cifran bajo estándares internacionales de protección, usados solo para su informe de evaluación y nunca compartidos con terceros o fines comerciales."}, {"question": "¿Qué incluye el informe de evaluación?", "answer": "El informe detallado incluye: (1) Evaluación e interpretación del índice de masa corporal y porcentaje de grasa corporal (2) Análisis completo de su salud (3) Plan personalizado de control de pérdida de peso (4) Consejos nutricionales y de ejercicio científicos (5) Recomendaciones para mejorar estilo de vida (6) Plan de seguimiento y monitoreo de progreso."}, {"question": "¿Cuándo reevaluar?", "answer": "Recomendamos reevaluaciones periódicas según su progreso de pérdida de peso. Durante ajustes activos de estilo de vida y hábitos alimentarios, evalúe mensualmente para seguir progresos. Si su peso es estable, evaluación trimestral o semestral basta para verificar eficacia del plan."}]}, "form": {"mainTitle": "Plan de Dieta Personalizado para Hipertensión", "subtitle": "¡Necesitamos el apoyo de sus datos de salud! Simplemente complete el cuestionario (aproximadamente 2 minutos), y podremos proporcionarle recomendaciones precisas de recetas bajas en sodio y altas en potasio para ayudarle a controlar la presión arterial de manera científica.", "step": "Paso", "ageLabel": "Edad", "years": "años", "prevButton": "Anterior", "nextButton": "Siguient<PERSON>", "submitButton": "Comenzar Personalización", "submittingButton": "Evaluando...", "loadExistingTitle": "Evaluación anterior encontrada", "loadExistingDesc": "Hemos encontrado un registro reciente de evaluación de salud, ¿desea cargarlo?", "loadExistingAction": "Cargar registro", "loadExistingCancel": "<PERSON><PERSON><PERSON> de nuevo", "submitSuccessTitle": "Evaluación exitosa", "submitSuccessDesc": "Su informe de salud ha sido generado.", "submitErrorAuthTitle": "Por favor inicie sesión primero", "submitErrorAuthDesc": "Por favor inicie sesión antes de realizar su evaluación de salud.", "submitErrorNetworkTitle": "Evaluación fallida", "submitErrorNetworkDesc": "El servidor tiene problemas, por favor intente de nuevo más tarde.", "questions": {"basicInfoTitle": "Información básica", "birthDate": {"title": "¿Cuál es su fecha de nacimiento?", "monthNames": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Abril", "Mayo", "<PERSON><PERSON>", "<PERSON>", "Agosto", "Septiembre", "Octubre", "Noviembre", "Diciembre"], "daySuffix": ""}, "gender": {"title": "¿Cuál es su género?", "male": "Hombre", "female": "<PERSON><PERSON>", "non_binary": "No binario"}, "height": {"title": "¿Cuál es su altura?", "unit": "cm"}, "weight": {"title": "¿Cuál es su peso?", "unit": "kg", "label": "Peso", "range": "Rango: {min} - {max} kg"}, "bloodPressure": {"title": "¿Cuál es su presión arterial actual?", "description": "Deslice para ajustar la presión sistólica y diastólica", "systolicLabel": "Presión sistólica", "diastolicLabel": "Presión diastólica", "unknownBloodPressure": "¿No conoce su presión arterial?", "normalBloodPressure": "Presión Arterial Normal", "highBloodPressure": "Presión Arterial Alta", "lowBloodPressure": "Presión Arterial Baja"}, "medicalHistory": {"title": "¿Tiene algún problema de salud?", "familyHistory": "Antecedentes familiares de hipertensión", "diabetes": "Diabetes", "heartDisease": "Enfermedad <PERSON>", "kidneyDisease": "Enfermedad renal", "stroke": "<PERSON><PERSON><PERSON> cerebral", "cholesterol": "Colesterol alto"}, "exercise": {"title": "¿Cuántas horas hace ejercicio por semana en promedio?", "none": "Sin ejercicio", "light": "1-2 horas/semana", "moderate": "3-4 horas/semana", "intense": "5+ horas/semana"}, "salt": {"title": "¿Cómo es su consumo diario de sal?", "low": "<PERSON>co salado", "normal": "Moderadamente salado", "high": "<PERSON><PERSON>o"}, "stress": {"title": "¿Cómo es su nivel de estrés emocional reciente?", "low": "<PERSON><PERSON>", "moderate": "Moderado", "high": "Alto"}, "sleep": {"title": "¿Cuántas horas duerme al día?", "unit": "horas", "short": "No duermo suficiente 😴", "good": "Duermo bien 😊", "long": "Duermo mucho 😌", "range_4": "4 horas", "range_8": "8 horas", "range_12": "12 horas"}, "additionalInfo": {"title": "¿Tiene alguna información de salud adicional que agregar?", "description": "por ejemplo: otras enfermedades, alergias, medicamentos a largo plazo, etc.", "placeholder": "Ingrese aquí..."}, "badHabits": {"title": "Todos tenemos algunos malos hábitos. ¿Cuáles son los tuyos?", "smoking": "Fumar frecuentemente", "drinking": "Beber frecuentemente", "stayingUpLate": "Trasnochar frecuentemente", "overeating": "Comer en exceso"}}}, "result": {"title": "Informe de Evaluación de Riesgo de Hipertensión", "description": "Evaluación personalizada basada en su información de salud", "reassess": "<PERSON><PERSON><PERSON><PERSON>", "riskAssessment": "I. Evaluación del Nivel de Riesgo", "mainRiskFactors": "Principales Factores de Riesgo", "bmi": "Índice de IMC", "currentBloodPressure": "Presión Arterial Actual", "systolic": "Sistólica", "diastolic": "Diastólica", "healthAnalysis": "II. <PERSON><PERSON><PERSON><PERSON> del Estado de Salud", "currentSymptoms": "(1) Síntomas actuales inferidos", "managementAdvice": "Consejos de manejo", "noSymptoms": "No se detectaron s<PERSON>as notables", "organDamageRisk": "Riesgo de daño orgánico", "lowRisk": "<PERSON><PERSON><PERSON> bajo", "moderateRisk": "<PERSON><PERSON><PERSON> moderado", "highRisk": "Riesgo alto", "possibleOrgans": "Órganos potencialmente afectados", "possibleComplications": "Posibles complicaciones", "nonDrugPlan": "3. Plan de control no farmacológico", "dietAdjustment": "(1) <PERSON><PERSON><PERSON>s <PERSON>", "dietPlanLoading": "Personalizando según su región...", "customDietPlan": "Plan nutricional personalizado", "dietAdvice": "Consejos alimenticios", "dietRestriction": "Restricciones dietéticas", "dietPlan": "Plan dietético detallado", "exerciseIntervention": "(2) Programa de ejercicio", "exercisePlanLoading": "Generando...", "customExercisePlan": "Plan de ejercicio personalizado", "recommendedExercise": "Tipos de ejercicio recomendados", "frequency": "Frecuencia", "duration": "Duración", "precautions": "Precauciones", "lifestyleAdjustment": "(3) Ajustes de estilo de vida", "sleepManagement": "Manejo <PERSON>ño", "stressManagement": "Manejo del estrés", "habitAdjustment": "Modificación de hábitos", "followUpAdvice": "Consejos de seguimiento", "monitoringIndicators": "Indicadores de monitoreo", "checkupAdvice": "Recomendaciones de chequeo", "emergencyIndicators": "Señales de emergencia que requieren consulta", "reassessConfirm": "⚠️ ¿Confirmar reevaluación? Esto borrará sus planes personalizados (dieta y ejercicio).", "loading": "Cargando su evaluación..."}, "dietPlan": {"title": "Plan Dietético Personalizado para Hipertensión", "subtitle": "Programa alimenticio individualizado basado en evaluación de riesgos", "planTitle": "Plan Dietético para Hipertensión", "planDescription": "Programa alimenticio individualizado basado en evaluación de riesgos", "dailyMealPlan": "Planificación de comidas", "calendarTitle": "Calendario Dietético", "calendarDescription": "Consulte el plan completo de 30 días y seleccione una fecha", "noDietPlanData": "Sin datos para el día {day}", "planStartsFromDay": "Plan inicia {day}, duración {total} días", "noMonthPlan": "Sin plan este mes", "generatingProgress": "⏳ Generado {current}/30 días", "weekDays": ["Dom", "<PERSON>n", "Mar", "<PERSON><PERSON>", "<PERSON><PERSON>", "Vie", "<PERSON><PERSON><PERSON>"], "today": "Hoy", "close": "<PERSON><PERSON><PERSON>", "nutritionTips": {"saltReduction": "Reducir sal disminuye presión", "balancedNutrition": "Dieta balanceada ayuda a controlar presión", "regularMeals": "Comidas regulares estabilizan presión"}, "foodCategories": {"staples": "Almidones", "protein": "<PERSON><PERSON><PERSON><PERSON>", "vegetables": "Vegetales", "fruits": "<PERSON><PERSON><PERSON>", "dairy": "Lácteos", "nuts": "<PERSON><PERSON><PERSON>"}, "dailyOverview": {"title": "Resumen nutricional diario", "nutritionMatch": "Balance nutricional", "totalCalories": "Calorías totales"}, "mealTimes": {"breakfast": "<PERSON><PERSON><PERSON>", "lunch": "<PERSON><PERSON><PERSON><PERSON>", "dinner": "<PERSON><PERSON>", "snack": "<PERSON><PERSON><PERSON>"}, "foodItems": {"oatmeal": "Avena", "boiledEgg": "<PERSON><PERSON> duro", "banana": "<PERSON><PERSON><PERSON><PERSON>", "apple": "Man<PERSON><PERSON>", "rice": "Arroz", "chicken": "Pollo", "fish": "Pescado", "vegetables": "Vegetales verdes", "tofu": "Tofu", "milk": "Leche", "yogurt": "<PERSON><PERSON><PERSON>"}, "nutritionInfo": {"calories": "Calorías", "kcal": "kcal", "protein": "<PERSON><PERSON><PERSON><PERSON>", "carbs": "Carbohidratos", "fat": "<PERSON><PERSON><PERSON>", "fiber": "Fibra", "sodium": "Sodio", "potassium": "Potasio"}, "actions": {"deleteAndRegenerate": "Borrar y regenerar", "backToResults": "Volver a resultados", "downloadPlan": "<PERSON><PERSON><PERSON>", "sharePlan": "Compartir", "viewMore": "<PERSON>er más", "refresh": "Actualizar"}, "timeFormats": {"morning": "7:30-8:00", "noon": "12:00-13:00", "evening": "18:30-19:30"}, "portions": {"times1": "×1", "times2": "×2", "times3": "×3", "serving": "porción", "cup": "taza", "piece": "unidad", "bowl": "tazón"}, "dayNumber": "<PERSON><PERSON> {day}", "monthDay": "{day}/{month}", "combinedWith": ", adaptado a {country}", "regionalSpecialty": "Sugerencia regional", "clickTip": "📖 Click en platillos para ver preparación | 🍚Almidones 🥩Proteínas 🥬Vegetales 🍎Frutas 🥜Otros", "deleteConfirm": "¿Confirmar borrado del plan dietético? Podrá generarse uno nuevo.", "deleteSuccess": "Plan borrado - listo para nueva generación", "deleteFailed": "Error al borrar:", "unknownError": "Error descon<PERSON>", "networkError": "Error de red al borrar"}, "exercisePlan": {"refresh": "Actualizar", "calendarTitle": "Calendario de Ejercicio", "calendarDescription": "Consulte el programa completo de 30 días y seleccione una fecha", "planStartsFromDay": "Programa inicia {day}, duración {total} días", "noMonthPlan": "Sin programa este mes - consulte otros meses", "generatingProgress": "⏳ Generando: {current}/30 días (actualice para ver progreso)", "weekDays": ["Dom", "<PERSON>n", "Mar", "<PERSON><PERSON>", "<PERSON><PERSON>", "Vie", "<PERSON><PERSON><PERSON>"], "today": "Hoy", "legend": {"today": "Hoy", "hasExercisePlan": "Con programa", "currentViewing": "Consultando"}, "noMonthExercise": "Sin programa este mes", "noMonthExerciseDescription": "Use flechas para consultar otros meses con programa", "exerciseArrangement": "Programa de ejercicio", "totalDuration": "Duración total", "intensity": {"low": "Baja", "medium": "Moderada", "high": "Alta"}, "heartRateControl": "Control de ritmo cardíaco", "targetHeartRate": "Objetivo:", "calculationMethod": "Mé<PERSON><PERSON> de cálculo:", "specificActions": "Ejercicios:", "duration": "Duración:", "sets": "Series:", "reps": "Repeticiones:", "precautions": "Precauciones", "requiredEquipment": "Equipo necesario:", "dailyTips": "Consejo del día", "exerciseGuidelines": "Recomendaciones", "exerciseContraindications": "Contraindicaciones", "progressiveAdvice": "Progresión recomendada", "generatedTime": "Generado el:", "clickTip": "🏃‍♂️ Click en ejercicios para instrucciones detalladas", "monthPlanTitle": "Programa de Ejercicio", "deleteConfirm": "¿Confirmar borrado del programa? Podrá generarse uno nuevo.", "deleteSuccess": "Programa borrado - listo para nueva generación", "deleteFailed": "Error al borrar:", "unknownError": "Error descon<PERSON>", "networkError": "Error de red al borrar", "deleteAndRegenerate": "🗑️ Borrar y regenerar", "backToResults": "← Volver a resultados", "close": "<PERSON><PERSON><PERSON>", "exerciseActionDialog": {"intensity": {"low": "Baja", "medium": "Moderada", "high": "Alta", "unknown": "Desconocida"}, "actionDescription": "Descripción", "detailedSteps": "Pasos detallados", "sets": "Series", "reps": "Repeticiones", "duration": "Duración", "heartRateControl": "Control de ritmo cardíaco", "targetHeartRate": "Objetivo:", "calculationMethod": "Mé<PERSON><PERSON> de cálculo:", "monitoringMethod": "Mé<PERSON>do de monitoreo:", "tips": "Consejos", "precautions": "Precauciones", "requiredEquipment": "Equipo necesario"}}, "toast": {"unlockFullDietPlan": "💡 Desbloquee plan completo (30 días)", "unlockFullDietPlanDesc": "Solo ve día 1. Suscríbase para acceder a 30 días personalizados y alcanzar sus metas de salud!", "unlockFullExercisePlan": "💡 Desbloquee programa completo (30 días)", "unlockFullExercisePlanDesc": "Solo ve día 1. Suscríbase para acceder a 30 días de consejos de ejercicio científicos!", "subscribe": "Suscribirse", "dietPlanUpdated": "✅ ¡Plan dietético actualizado!", "dietPlanUpdatedDesc": "{count} días añadidos - total {total}/30 días.", "exercisePlanUpdated": "✅ ¡Programa de ejercicio actualizado!", "exercisePlanUpdatedDesc": "{count} días añadidos - total {total}/30 días.", "stillGenerating": "⏳ Generando...", "dietPlanGeneratingDesc": "Su plan dietético se está generando - {completed}/30 días listos - vuelva más tarde.", "exercisePlanGeneratingDesc": "Su programa de ejercicio se está generando - {completed}/30 días listos - vuelva más tarde.", "planIsLatest": "👍 Plan actualizado", "dietPlanLatestDesc": "Su plan dietético está completo ({total}/30 días).", "exercisePlanLatestDesc": "Su programa de ejercicio está completo ({total}/30 días).", "noPlanToRefresh": "ℹ️ Nada que actualizar", "noDietPlanDesc": "Genere primero un plan dietético.", "noExercisePlanDesc": "Genere primero un programa de ejercicio.", "generateDietPlanFailed": "Error al generar", "generateExercisePlanFailed": "Error al generar", "networkError": "Error de red - reintente", "refreshFailed": "<PERSON><PERSON>r al actualizar - reintente", "dietPlanGenerating": "📋 Generando", "dietPlanGeneratingInProgress": "Su plan dietético se está generando ({completed}/30 días) - espere o actualice luego!", "exercisePlanGenerating": "🏃‍♂️ Generando", "exercisePlanGeneratingInProgress": "Su programa de ejercicio se está generando ({completed}/30 días) - espere o actualice luego!", "assessmentFailed": "Error en evaluación", "networkErrorRetry": "Error de red - reintente"}, "riskLevels": {"low": "<PERSON><PERSON>", "moderate": "Moderado", "high": "Alto", "very_high": "<PERSON><PERSON> alto", "unknown": "Desconocido"}, "foodCategories": {"staples": "Almidones", "protein": "<PERSON><PERSON><PERSON><PERSON>", "vegetables": "Vegetales", "fruits": "<PERSON><PERSON><PERSON>", "others": "<PERSON><PERSON><PERSON>", "nutritionMatch": "🍽️ Balance nutricional", "viewRecipe": "Ver receta"}, "foodRecipe": {"basicInfo": "Información básica", "category": "Categoría:", "quantity": "Cantidad:", "calories": "Calorías:", "ingredients": "Ingredientes", "steps": "Preparación", "tips": "Consejos clave", "nutritionValue": "Valor nutricional", "difficulty": {"easy": "F<PERSON><PERSON>l", "medium": "Medio", "hard": "Dif<PERSON><PERSON>l"}, "kcal": "kcal"}}