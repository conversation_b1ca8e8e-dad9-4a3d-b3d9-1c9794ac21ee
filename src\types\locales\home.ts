export interface Home {
    meta: {
        keywords: string;
        title: string;
        description: string;
        alt: string;
    };
    hero: {
        aiLabel: string;
        title: string;
        subtitle: string;
        description: string;
        ctaPrimary: string;
        ctaSecondary: string;
        features: Array<{
            title: string;
            description: string;
        }>;
    };
    pyramid: {
        title: string;
        subtitle: string;
        managementLevels: string;
        fromAcuteToPrevention: string;
        healthScore: string;
        healthScoreLabel: string;
        levels: Array<{
            level: number;
            label: string;
            percentage: string;
        }>;
        pyramidLevels: Array<{
            title: string;
            subtitle: string;
            level: string;
            category: string;
            systolic: string;
            diastolic: string;
            intervention: string;
            systolicUnit: string;
            diastolicUnit: string;
            interventionUnit: string;
        }>;
        description: string;
        stats: Array<{
            number: string;
            label: string;
        }>;
    };
    statistics: {
        stats: Array<{
            number: string;
            label: string;
        }>;
    };
    assessment: {
        title: string;
        subtitle: string;
    };
    cases: {
        title: string;
        subtitle: string;
        examples: Array<{
            icon: string;
            title: string;
            description: string;
            result: string;
            resultLabel: string;
        }>;
        buttons: {
            startAssessment: string;
            viewMoreCases: string;
        };
        disclaimer: string;
    };
    process: {
        title: string;
        subtitle: string;
        steps: Array<{
            title: string;
            desc: string;
            step: string;
        }>;
    };
    advantages: {
        title: string;
        subtitle: string;
        features: Array<{
            title: string;
            subtitle: string;
            description: string;
            tags: string[];
        }>;
        highlights: Array<{
            title: string;
            description: string;
        }>;
    };
    faq: {
        title: string;
        subtitle: string;
        items: Array<{
            question: string;
            answer: string;
        }>;
    };
    form: {
        title: string;
        stepIndicator: string;
        steps: {
            basicInfo: {
                title: string;
                description: string;
                age: string;
                agePlaceholder: string;
                gender: string;
                genderPlaceholder: string;
                male: string;
                female: string;
                height: string;
                heightPlaceholder: string;
                weight: string;
                weightPlaceholder: string;
                bmi: string;
            };
            bloodPressure: {
                title: string;
                description: string;
                currentBloodPressure: string;
                systolic: string;
                systolicPlaceholder: string;
                diastolic: string;
                diastolicPlaceholder: string;
                recentBloodPressure: string;
                morning: string;
                evening: string;
            };
            medicalHistory: {
                title: string;
                description: string;
                diabetes: string;
                heartDisease: string;
                kidneyDisease: string;
                stroke: string;
                cholesterolHigh: string;
                otherDiseases: string;
                otherDiseasesPlaceholder: string;
            };
            familyHistory: {
                title: string;
                description: string;
                slimmerplan: string;
                heartDisease: string;
                diabetes: string;
                stroke: string;
            };
            medications: {
                title: string;
                description: string;
                currentMedications: string;
                antihypertensive: string;
                antihypertensivePlaceholder: string;
                otherMedications: string;
                otherMedicationsPlaceholder: string;
                allergies: string;
                medicationAllergies: string;
                medicationAllergiesPlaceholder: string;
                foodAllergies: string;
                foodAllergiesPlaceholder: string;
                otherAllergies: string;
                otherAllergiesPlaceholder: string;
            };
            lifestyle: {
                title: string;
                description: string;
                smoking: string;
                smokingOptions: {
                    never: string;
                    former: string;
                    current: string;
                };
                alcohol: string;
                alcoholOptions: {
                    never: string;
                    occasional: string;
                    regular: string;
                    heavy: string;
                };
                exercise: string;
                exerciseOptions: {
                    none: string;
                    light: string;
                    moderate: string;
                    intense: string;
                };
                salt: string;
                saltOptions: {
                    low: string;
                    normal: string;
                    high: string;
                };
                stress: string;
                stressOptions: {
                    low: string;
                    moderate: string;
                    high: string;
                };
                sleep: string;
            };
        };
        navigation: {
            previous: string;
            next: string;
            submit: string;
            loading: string;
        };
    };
    // 动物识别结果相关接口 - 这部分在 animal-detail-tabs.tsx 中实际使用
    results?: {
        tabs?: {
            basic?: string;
            lifestyle?: {
                label?: string;
            };
            advanced?: {
                label?: string;
            };
        };
        basicInfo?: {
            title?: string;
            category?: string;
            family?: string;
            weight?: string;
            lifespan?: string;
            diet?: string;
            temperament?: {
                title?: string;
                docilityIndex?: string;
                aggressivenessIndex?: string;
                description?: string;
            };
            domestication?: {
                title?: string;
                suitabilityTypes?: string;
                bestScenarios?: string;
                careLevel?: string;
                legalStatus?: string;
            };
            behavior?: string;
            appearance?: {
                title?: string;
                size?: string;
                bodyShape?: string;
                colorPattern?: string;
                specialStructures?: string;
            };
        };
        habitat?: {
            title?: string;
            regions?: string;
            nativeHabitat?: string;
            preferredEnvironment?: string;
        };
        conservation?: {
            title?: string;
            threats?: string;
        };
        recommendations?: {
            similarSpecies?: string;
            observationTips?: string;
            conservationActions?: string;
        };
        interestingFacts?: string;
        uniqueFeatures?: {
            title?: string;
            environmentalAdaptation?: {
                label?: string;
                specialSkills?: string;
                adaptations?: string;
            };
            communication?: {
                label?: string;
                vocalizations?: string;
                specialCommunication?: string;
            };
            migration?: {
                label?: string;
                migratory?: string;
                pattern?: string;
                reason?: string;
                nonMigratory?: string;
            };
        };
        interspeciesRelations?: {
            title?: string;
            symbiotic?: string;
            parasitic?: string;
            competitive?: string;
            predatorPrey?: string;
        };
        lifestyle?: {
            healthAssessment?: {
                title?: string;
                physicalCondition?: string;
                behaviorAnalysis?: string;
                environmentalImpact?: string;
                nutritionalNeeds?: string;
                commonDiseases?: string;
                preventiveCare?: string;
            };
            healthStatus?: {
                title?: string;
                overallHealth?: string;
                furCondition?: string;
                bodyPosture?: string;
                physicalAbnormalities?: string;
                healthRecommendations?: string;
            };
            mentalState?: {
                title?: string;
                alertnessLevel?: string;
                activityLevel?: string;
                emotionalState?: string;
                stressSigns?: string;
                mentalHealthTips?: string;
            };
            growthCycle?: {
                title?: string;
                estimatedAge?: string;
                growthStage?: string;
                developmentProgress?: string;
                nextStagePreview?: string;
                careGuidance?: string;
            };
            lifeHabits?: {
                title?: string;
                dailyActivity?: string;
                huntingFeeding?: string;
                socialBehavior?: string;
                territorialBehavior?: string;
            };
            unlock?: {
                title?: string;
                description?: string;
            };
        };
        advancedAnalysis?: {
            videoContent?: {
                title?: string;
                animalInteractions?: string;
                behaviorSequence?: string;
                environmentalContext?: string;
                mainEvents?: string;
            };
            behaviorAnalysis?: {
                title?: string;
                socialDynamics?: string;
                territorialBehavior?: string;
                feedingBehavior?: string;
                dominantBehaviors?: string;
                conflictBehaviors?: string;
            };
        };
    };
    buttons?: {
        unlockHealth?: {
            loading?: string;
            default?: string;
        };
    };
    status?: {
        pointsCost?: string;
        creditBalance?: string;
    };
}
