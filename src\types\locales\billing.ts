export interface Billing {
    page: {
      title: string;
      description: string;
    };
    subscription: SubscriptionLocal;
    transaction: {
      title: string;
      description: string;
      productName: string;
      unit: string;
      price: string;
      date: string;
      status: string;
      noTransactions: string;
    };
    toast: ToastLocal;
    meta:{
        title: string;
        description: string;
    };
  }

  export interface SubscriptionLocal  {
    title: string;
    description: string;
    currentPlan: {
      planName: string;
      billingCycle: string;
      monthlyUnit: string;
      quarterlyUnit: string;
      annualUnit: string;
      subscriptionStart: string;
      nextRenewal: string;
      noSubscriptions: string;
      cancelButton: string;
    };
    cancelDialog: {
      title: string;
      description: string;
      cancelButton: string;
      confirmButton: string;
    };
  }

  export interface ToastLocal {
    error: {
        title: string;
        loadData: string;
        cancelSubscription: string;
      };
      success: {
        title: string;
        cancelSubscription: string;
      };
  }