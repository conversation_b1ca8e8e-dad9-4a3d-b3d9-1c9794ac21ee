export interface Pricing {
  hero: {
    subtitle: string;
    title: string;
    description: string;
  };
  plan: {
    title: string;
    price: {
      amount: string;
      period: string;
    };
    currency: string;
    mode: "payment" | "recurring";
    features: Array<string>;
    product: Product;
    cta: string;
  };
  quarterly: {
    title: string;
    price: {
      amount: string;
      period: string;
    };
    currency: string;
    mode: "payment" | "recurring";
    features: Array<string>;
    product: Product;
    cta: string;
  };
  annual: {
    title: string;
    price: {
      amount: string;
      period: string;
    };
    currency: string;
    mode: "payment" | "recurring";
    features: Array<string>;
    product: Product;
    cta: string;
  };
  oneTime?: {
    title: string;
    price: {
      amount: string;
      period: string;
    };
    features?: Array<string>;
    product: Product;
    cta: string;
  };
  paymentTips: string;
  errors?: {
    activeSubscriptionExists: string;
    subscriptionAlreadyActive: string;
  };
  trust: {
    text: string;
  };
  meta: {
    keywords: string;
    title: string;
    description: string;
    alt: string;
  }
}

export interface Product {
  code: string;
  desc: string;
  name: string;
  quantity: string;
  sku: string;
  type: string;
  unit_price: string;
  url?: string;
  priceId?: string;
}