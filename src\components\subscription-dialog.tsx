'use client';
import { useState, useEffect } from 'react';
import { findSubscriptionByUserId } from '@/actions/user-order';
import { SubscriptionLocal, ToastLocal } from "@/types/locales/billing";
import { getDictionary, i18nNamespaces } from '@/i18n';
import { type Locale } from '@/i18n-config';
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast"

// 订阅对话框翻译接口
interface SubscriptionDialogTranslations {
  title: string
  description: string
  loading: string
  cumulativeSubscription: {
    title: string
    subscriptionCount: string
    autoRenewal: string
    renewalStopped: string
    cumulativePrice: string
    cumulativeRenewalDate: string
    cumulativeExpiryDate: string
  }
  status: {
    renewalCancelled: string
  }
  currentPlan: {
    planName: string
    billingCycle: string
    monthlyUnit: string
    quarterlyUnit: string
    annualUnit: string
    subscriptionStart: string
    nextRenewal: string
    expiryDate: string
    noSubscriptions: string
    cancelButton: string
  }
  cancelDialog: {
    title: string
    description: string
    cancelButton: string
    confirmButton: string
  }
  toast: {
    error: {
      title: string
      cancelSubscription: string
    }
    success: {
      title: string
      cancelSubscription: string
    }
  }
}

interface Subscription {
    orderId: string;
    credits: string;
    subscriptionType: string;
    price: string;
    date: string;
    renewalDate: string;
    isActive: boolean;
    status: 'active' | 'cancelled';
    // 累积订阅相关字段
    isCumulative?: boolean;
    totalSubscriptions?: number;
    cumulativeExpiryDate?: string;
}

interface ManageSubscriptionDialogProps {
    i18n?: SubscriptionLocal;
    toastLocal: ToastLocal;
    onClose: () => void;
    open: boolean;
    lang: Locale;
}

export function ManageSubscriptionDialog({ i18n, toastLocal, onClose, open, lang }: ManageSubscriptionDialogProps) {
    const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
    const [loading, setLoading] = useState(true);
    const [confirmDialog, setConfirmDialog] = useState<{ show: boolean; orderId: string }>({
        show: false,
        orderId: ''
    });
    const [cancellingOrderId, setCancellingOrderId] = useState<string>('');
    const [isConfirming, setIsConfirming] = useState(false);
    const [translations, setTranslations] = useState<SubscriptionDialogTranslations | null>(null);
    const { toast } = useToast();

    // 获取翻译
    useEffect(() => {
        const loadTranslations = async () => {
            try {
                const t = await getDictionary<SubscriptionDialogTranslations>(lang, i18nNamespaces.subscriptionDialog);
                setTranslations(t);
            } catch (error) {
                console.error('Failed to load subscription dialog translations:', error);
            }
        };
        loadTranslations();
    }, [lang]);

    useEffect(() => {
        const fetchSubscriptions = async () => {
            try {
                const data = await findSubscriptionByUserId();
                setSubscriptions(data || []);
            } catch (error) {
                console.error('Failed to fetch subscription data:', error);
                toast({
                    title: toastLocal.error?.title,
                    description: toastLocal?.error?.cancelSubscription,
                    variant: "destructive",
                    duration: 3000,
                    position: "top-center",
                  })
            } finally {
                setLoading(false);
            }
        };

        if (open) {
            fetchSubscriptions();
        }
    }, [open]);

    const handleCancelClick = (orderId: string) => {
        setCancellingOrderId(orderId);
        setConfirmDialog({ show: true, orderId });
    };

    const handleConfirmCancel = async () => {
        setIsConfirming(true);
        try {
            const subscriptionId = confirmDialog.orderId;
            const response = await fetch('/api/subscription', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ subscriptionId }),
            });

            if (response.status === 200) {
                setSubscriptions(subscriptions.filter(sub => sub.orderId !== confirmDialog.orderId));
                toast({
                    title: toastLocal.success?.title,
                    description: toastLocal.success?.cancelSubscription,
                    duration: 3000,
                    position: "top-center",
                  })
            } else {
                const errorData = await response.json();
                toast({
                    title: toastLocal.error?.title,
                    description: toastLocal.error?.cancelSubscription,
                    variant: "destructive",
                    duration: 3000,
                    position: "top-center",
                  })
            }
        } catch (error) {
            console.error('Failed to cancel subscription:', error);
            toast({
                title: toastLocal.error?.title,
                description: toastLocal.error?.cancelSubscription,
                variant: "destructive",
                duration: 3000,
                position: "top-center",
              })
        } finally {
            setIsConfirming(false);
            setCancellingOrderId('');
            setConfirmDialog({ show: false, orderId: '' });
        }
    };

    const cancelHandle = () => {
        setIsConfirming(false);
        setCancellingOrderId('');
        setConfirmDialog({ show: false, orderId: '' });
    }
    return (
        <>
            <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
                <DialogContent className="sm:max-w-2xl">
                    <DialogHeader>
                        <DialogTitle className="text-xl font-semibold text-gray-900 dark:text-white">
                            {i18n?.description}
                        </DialogTitle>
                    </DialogHeader>
                    
                    <div className="border border-primary-gold/20 dark:border-primary-gold/30 rounded-lg p-6 max-h-[80vh] overflow-y-auto">
                        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                            {i18n?.title}
                        </h2>
                        <Separator className="my-4 bg-primary-gold/20 dark:bg-primary-gold/30" />
                        
                        <div className="grid grid-rows-1 md:grid-cols-1 gap-4">
                            {loading ? (
                                <div className="flex flex-col items-center justify-center w-full py-8">
                                    <div className="animate-spin rounded-full h-8 w-8 border-4 border-primary-gold border-t-transparent"></div>
                                    <p className="mt-4 text-gray-600 dark:text-gray-300">{translations?.loading || 'Loading...'}</p>
                                </div>
                            ) : subscriptions.length > 0 ? (
                                subscriptions.map((subs, index) => (
                                    <div key={index} className="space-y-4 py-4 border-b border-primary-gold/20 dark:border-primary-gold/30 last:border-b-0">
                                        <div className="flex items-center justify-between">
                                            <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                                                {subs.isCumulative && subs.totalSubscriptions && subs.totalSubscriptions > 1 ?
                                                    `${translations?.cumulativeSubscription.title || '累积订阅计划'} (${subs.totalSubscriptions}${translations?.cumulativeSubscription.subscriptionCount || '个订阅'}${subs.isActive ? `，${translations?.cumulativeSubscription.autoRenewal || '自动续费'}` : `，${translations?.cumulativeSubscription.renewalStopped || '已停止续费'}`})` :
                                                    i18n?.currentPlan?.planName.replace('[0]',
                                                        subs.subscriptionType === 'monthly' ? i18n?.currentPlan?.monthlyUnit || '月度订阅' :
                                                        subs.subscriptionType === 'quarterly' ? i18n?.currentPlan?.quarterlyUnit || '季度订阅' :
                                                        subs.subscriptionType === 'annual' ? i18n?.currentPlan?.annualUnit || '年度订阅' :
                                                        '订阅计划'
                                                    )
                                                }
                                            </h4>
                                            {!subs.isActive && (
                                                <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 rounded-full">
                                                    {translations?.status.renewalCancelled || '续费已取消'}
                                                </span>
                                            )}
                                        </div>
                                        <dl className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                            <div>
                                                <dt className="text-sm text-gray-500 dark:text-gray-400">
                                                    {subs.isCumulative && subs.totalSubscriptions && subs.totalSubscriptions > 1 ?
                                                        (translations?.cumulativeSubscription.cumulativePrice || '累积总价格') : i18n?.currentPlan?.billingCycle
                                                    }
                                                </dt>
                                                <dd className="text-base font-medium text-gray-900 dark:text-white">
                                                    {subs.isCumulative && subs.totalSubscriptions && subs.totalSubscriptions > 1 ?
                                                        subs.price :
                                                        `${subs.price} / ${
                                                            subs.subscriptionType === 'monthly' ? i18n?.currentPlan?.monthlyUnit :
                                                            subs.subscriptionType === 'quarterly' ? i18n?.currentPlan?.quarterlyUnit :
                                                            subs.subscriptionType === 'annual' ? i18n?.currentPlan?.annualUnit :
                                                            i18n?.currentPlan?.monthlyUnit
                                                        }`
                                                    }
                                                </dd>
                                            </div>
                                            <div>
                                                <dt className="text-sm text-gray-500 dark:text-gray-400">{i18n?.currentPlan?.subscriptionStart}</dt>
                                                <dd className="text-base font-medium text-gray-900 dark:text-white">
                                                    {new Date(subs.date).toLocaleString()}
                                                </dd>
                                            </div>
                                            <div>
                                                <dt className="text-sm text-gray-500 dark:text-gray-400">
                                                    {subs.isCumulative && subs.totalSubscriptions && subs.totalSubscriptions > 1 ?
                                                        (subs.isActive ? (translations?.cumulativeSubscription.cumulativeRenewalDate || '累积续订日期') : (translations?.cumulativeSubscription.cumulativeExpiryDate || '累积到期日期')) :
                                                        (subs.isActive ? i18n?.currentPlan?.nextRenewal : (translations?.currentPlan.expiryDate || '到期日期'))
                                                    }
                                                </dt>
                                                <dd className="text-base font-medium text-gray-900 dark:text-white">
                                                    {new Date(subs.renewalDate).toLocaleDateString()}
                                                </dd>
                                            </div>
                                        </dl>
                                        {subs.isActive && (
                                            <div className="pt-2">
                                                <Button
                                                    variant="destructive"
                                                    size="sm"
                                                    onClick={() => handleCancelClick(subs.orderId)}
                                                    disabled={cancellingOrderId === subs.orderId}
                                                    className="flex items-center"
                                                >
                                                    {cancellingOrderId === subs.orderId ? (
                                                        <>
                                                            <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                                                            {i18n?.currentPlan?.cancelButton}
                                                        </>
                                                    ) : (
                                                        <>
                                                            <X className="h-4 w-4 mr-2" />
                                                            {i18n?.currentPlan?.cancelButton}
                                                        </>
                                                    )}
                                                </Button>
                                            </div>
                                        )}
                                    </div>
                                ))
                            ) : (
                                <div className="flex justify-center items-center py-8">
                                    <p className="text-gray-700 dark:text-gray-400">{i18n?.currentPlan?.noSubscriptions}</p>
                                </div>
                            )}
                        </div>
                    </div>
                </DialogContent>
            </Dialog>
    
            {confirmDialog.show && (
                <Dialog open={confirmDialog.show} onOpenChange={(open) => !open && cancelHandle()}>
                    <DialogContent className="sm:max-w-md">
                        <DialogHeader>
                            <DialogTitle className="text-lg font-semibold text-gray-900 dark:text-white">
                                {i18n?.cancelDialog?.title}
                            </DialogTitle>
                        </DialogHeader>
                        <p className="text-gray-600 dark:text-gray-300 mb-6">
                            {i18n?.cancelDialog?.description}
                        </p>
                        <div className="flex justify-end space-x-4">
                            <Button
                                variant="outline"
                                onClick={cancelHandle}
                                disabled={isConfirming}
                                className="border-primary-gold/20 dark:border-primary-gold/30"
                            >
                                {i18n?.cancelDialog?.cancelButton}
                            </Button>
                            <Button
                                variant="destructive"
                                onClick={handleConfirmCancel}
                                disabled={isConfirming}
                                className="flex items-center"
                            >
                                {isConfirming && (
                                    <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                                )}
                                {i18n?.cancelDialog?.confirmButton}
                            </Button>
                        </div>
                    </DialogContent>
                </Dialog>
            )}
        </>
    );
}