import { MetadataRoute } from 'next'
import { host } from '@/config/config'
import { localizationsKV } from '@/i18n-config'
import { getDictionary, i18nNamespaces } from '@/i18n'
import { generateBlogPosts } from '@/data/blog-posts'
import type { About } from '@/types/locales/about'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
    const commonPages = [
        '/contact',
        '/pricing',
        '/about'
    ]

    const singleUrls = [
        '/legal/privacy',
        '/legal/terms',
        '/legal/payments-refund'
    ];

    const generateLocalizedUrls = (path: string) => {
        return Object.keys(localizationsKV).map((lang) => ({
            url: lang === 'en'
                ? `${host}${path}`
                : `${host}/${lang}${path}`,
            lastModified: new Date(),
            priority: path === '' ? 1.0 : (lang === 'en' ? 0.8 : 0.64),
        }))
    }

    const generateSingleUrl = (path: string) => ({
        url: `${host}${path}`,
        lastModified: new Date(),
        priority: 0.64,
    })

    // 获取博客文章的 slug 列表
    const getBlogPostUrls = async () => {
        try {
            // 获取默认语言的文章列表
            const i18n: About = await getDictionary<About>('en', i18nNamespaces.about);
            const blogPosts = generateBlogPosts(i18n);
            const slugs = Object.keys(blogPosts);

            // 为每个语言和文章生成 URL
            const blogUrls: MetadataRoute.Sitemap = [];

            Object.keys(localizationsKV).forEach((lang) => {
                slugs.forEach((slug) => {
                    blogUrls.push({
                        url: lang === 'en'
                            ? `${host}/about/${slug}`
                            : `${host}/${lang}/about/${slug}`,
                        lastModified: new Date(),
                        priority: 0.6,
                    });
                });
            });

            return blogUrls;
        } catch (error) {
            console.error('Error generating blog post URLs for sitemap:', error);
            return [];
        }
    }

    const blogPostUrls = await getBlogPostUrls();

    const sitemapEntries: MetadataRoute.Sitemap = [
        ...generateLocalizedUrls(''),
        ...commonPages.flatMap(page => generateLocalizedUrls(`${page}`)),
        ...singleUrls.map(url => generateSingleUrl(url)),
        ...blogPostUrls
    ]

    return sitemapEntries
}