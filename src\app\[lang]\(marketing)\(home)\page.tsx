import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Brain,
  Info,
  User,
  Target,
  CheckCircle,
  Users,
  Zap,
  Scale,
  Apple,
  Dumbbell,
  TrendingDown
} from "lucide-react"
import { Accordion, AccordionContent, AccordionTrigger, AccordionItem } from "@/components/ui/accordion"

import { type Locale, getPathname, generateAlternates } from "@/i18n-config";
import { getDictionary, i18nNamespaces } from '@/i18n'
import { Home as HomeType } from "@/types/locales";

import { host } from '@/config/config'
// 完全移除客户端特有的hooks导入
// import { useRef } from 'react';
// import { useSession } from 'next-auth/react';


import { cookies } from 'next/headers'
import { Suspense } from 'react'

export const dynamic = 'force-dynamic'

export async function generateMetadata({ params }: { params: { lang: Locale } }) {
  const { lang } = await params
  const alternates = generateAlternates(lang, '/');
  const i18nHome = await getDictionary<HomeType>(lang, i18nNamespaces.home);
  return {
    title: i18nHome.meta.title,
    description: i18nHome.meta.description,
    keywords: i18nHome.meta.keywords,
    twitter: {
      card: "summary_large_image", 
      title: i18nHome.meta.title,
      description: i18nHome.meta.description
    },
    openGraph: {
      type: "website",
      url: `${host}${getPathname(lang, '')}`,
      title: i18nHome.meta.title,
      description: i18nHome.meta.description,
      siteName: i18nHome.hero.title
    },
    alternates: {
      canonical: `${host}${getPathname(lang, '')}`,
      languages: alternates
    }
  }
}

export default async function Home({
  params,
}: {
  params: { lang: string }
}) {
  const { lang } = await params
  const i18nHome = await getDictionary<HomeType>(lang, i18nNamespaces.home);

  
  // 检查用户登录状态和信息完善状态
  // 实际应用中，这部分逻辑可能需要根据您的认证系统进行调整
  let isLoggedIn = false
  try {
    const cookieStore = await cookies()
    
    // 尝试多种可能的认证cookie名称
    isLoggedIn = cookieStore.has('auth_token') ||
                cookieStore.has('next-auth.session-token') ||
                cookieStore.has('__Secure-next-auth.session-token') ||
                cookieStore.has('session-id')
  } catch (error) {
    console.error('检查登录状态出错:', error)
  }
  
  // 注意：这是一个服务器组件，我们在这里准备客户端组件所需的数据，
  // 实际的交互逻辑将在客户端组件中处理

  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-slate-50 via-green-50/30 to-blue-50/20">
      {/* 健身瘦身主题背景装饰 */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        {/* 运动轨迹波形背景 */}
        <div className="absolute top-0 left-0 w-full h-full opacity-[0.02]">
          <svg className="w-full h-full" viewBox="0 0 1200 600" preserveAspectRatio="none">
            <path d="M0,400 Q100,350 200,380 T400,360 Q500,320 600,340 T800,320 Q900,280 1000,300 T1200,280"
                  stroke="currentColor"
                  strokeWidth="3"
                  fill="none"
                  className="text-green-600"/>
            <path d="M0,450 Q150,400 300,430 T600,410 Q750,370 900,390 T1200,370"
                  stroke="currentColor"
                  strokeWidth="2"
                  fill="none"
                  className="text-blue-500 opacity-60"/>
          </svg>
        </div>

        {/* 健身图标装饰 */}
        <div className="absolute top-20 left-10 w-16 h-16 bg-green-100 rounded-full flex items-center justify-center opacity-20 pulse-slow">
          <Dumbbell className="w-8 h-8 text-green-600" />
        </div>
        <div className="absolute top-32 right-16 w-14 h-14 bg-blue-100 rounded-full flex items-center justify-center opacity-20 float-animation">
          <Target className="w-7 h-7 text-blue-600" />
        </div>
        <div className="absolute bottom-32 right-20 w-20 h-20 bg-emerald-100 rounded-full flex items-center justify-center opacity-20 pulse-slow">
          <Zap className="w-10 h-10 text-emerald-600" />
        </div>
        <div className="absolute bottom-64 left-12 w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center opacity-20 float-delayed">
          <TrendingDown className="w-6 h-6 text-orange-600" />
        </div>
        <div className="absolute top-1/2 left-1/3 w-18 h-18 bg-purple-100 rounded-full flex items-center justify-center opacity-15 pulse-slow">
          <Scale className="w-9 h-9 text-purple-600" />
        </div>
      </div>

      {/* Hero区域 - 重新设计 */}
      <section className="flex items-center justify-center relative px-4 py-16 pt-4">
        <div className="max-w-7xl mx-auto text-center space-y-8 w-full">
          {/* AI智能标签 - 全新高级设计 */}
          <div className="relative group">
            {/* 背景光晕效果 */}
            <div className="absolute inset-0 bg-gradient-to-r from-violet-600/20 via-blue-600/20 to-cyan-600/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-500 scale-110"></div>

            {/* 主标签容器 */}
            <div className="relative inline-flex items-center bg-white/90 backdrop-blur-2xl border-2 border-white/50 px-8 py-4 rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-500 hover:-translate-y-1 overflow-hidden">
              {/* 内部渐变背景 */}
              <div className="absolute inset-0 bg-gradient-to-r from-violet-50/80 via-blue-50/60 to-cyan-50/80 opacity-60"></div>

              {/* 动态边框光效 */}
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-violet-500 via-blue-500 to-cyan-500 opacity-0 group-hover:opacity-30 transition-opacity duration-500" style={{padding: '1px'}}>
                <div className="w-full h-full bg-transparent rounded-2xl"></div>
              </div>

              {/* 图标和文字 */}
              <div className="relative flex items-center">
                <div className="relative mr-4">
                  <Sparkles className="w-6 h-6 text-violet-600 animate-pulse relative z-10" />
                  <div className="absolute inset-0 w-6 h-6 bg-violet-400 rounded-full opacity-20 animate-ping"></div>
                </div>
                <span className="text-lg font-bold bg-gradient-to-r from-violet-700 via-blue-700 to-cyan-700 bg-clip-text text-transparent tracking-wide">
                  {i18nHome.hero.aiLabel||"AI智能瘦身评估"}
                </span>
              </div>

              {/* 装饰性粒子 */}
              <div className="absolute top-1 right-2 w-2 h-2 bg-gradient-to-r from-violet-400 to-blue-400 rounded-full opacity-60 animate-bounce"></div>
              <div className="absolute bottom-1 left-3 w-1.5 h-1.5 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full opacity-40 animate-pulse"></div>
            </div>
          </div>

          {/* 主标题 - 全新设计 */}
          <div className="space-y-8 relative">
            {/* 背景装饰元素 */}
            <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 w-32 h-32 bg-gradient-to-r from-violet-200/30 to-cyan-200/30 rounded-full blur-3xl opacity-50"></div>

            <div className="relative">
              <h1 className="text-5xl md:text-7xl font-black leading-tight tracking-tight">
                {/* 主标题 - 立体渐变效果 */}
                <span className="block relative">
                  <span className="absolute inset-0 bg-gradient-to-r from-slate-400 via-slate-300 to-slate-400 bg-clip-text text-transparent blur-sm opacity-40">
                    {i18nHome.hero.title||"健康瘦身评估"}
                  </span>
                  <span className="relative bg-gradient-to-r from-slate-700 via-violet-600 to-blue-600 bg-clip-text text-transparent break-keep">
                    {i18nHome.hero.title||"健康瘦身评估"}
                  </span>

                  {/* 标题装饰线 */}
                  <div className="absolute -bottom-4 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-violet-400/60 to-transparent rounded-full"></div>
                </span>

                {/* 副标题 - 现代排版 */}
                <span className="block text-lg md:text-2xl mt-8 font-medium relative">
                  <span className="inline-flex flex-wrap items-center justify-center gap-4 gap-y-6 text-slate-600">
                    {(i18nHome.hero.subtitle || "专业 · 智能 · 个性化体重管理方案").split(' · ').map((item, index) => (
                      <span key={index} className="relative">
                        <span className={`px-4 py-2 bg-gradient-to-r ${
                          index === 0 ? 'from-violet-100 to-blue-100 border-violet-200/50' :
                          index === 1 ? 'from-blue-100 to-cyan-100 border-blue-200/50' :
                          'from-cyan-100 to-emerald-100 border-cyan-200/50'
                        } rounded-xl border shadow-sm transition-all duration-300 hover:shadow-md hover:scale-105`}>
                          {item.trim()}
                        </span>
                        {index < 2 && (
                          <span className={`absolute -right-6 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-gradient-to-r ${
                            index === 0 ? 'from-violet-400 to-blue-400' : 'from-blue-400 to-cyan-400'
                          } rounded-full animate-pulse`}></span>
                        )}
                      </span>
                    ))}
                  </span>
                </span>
              </h1>
            </div>

            {/* 描述文字 - 优雅设计 */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/50 to-transparent rounded-2xl blur-sm"></div>
              <p className="relative text-lg md:text-xl text-slate-600 max-w-5xl mx-auto leading-relaxed bg-white/60 backdrop-blur-sm rounded-2xl px-8 py-6 border border-white/30 shadow-lg">
                {i18nHome.hero.description.split('\n').map((line, index) => (
                  <span key={index} className="block">
                    {line}
                    {index === 0 && <br className="hidden md:block" />}
                  </span>
                ))}
              </p>
            </div>
          </div>

          {/* CTA按钮组 */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-6 pt-4">
            <a href="#health-assessment">
              <button className="group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-700 to-blue-800 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative flex items-center">
                  <Brain className="w-5 h-5 mr-2" />
                  {i18nHome.hero.ctaPrimary||"开始瘦身评估"}
                </div>
              </button>
            </a>
            <button className="px-8 py-4 bg-white/80 backdrop-blur-sm border border-slate-200 text-slate-700 font-semibold rounded-2xl shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
              <Info className="w-5 h-5 mr-2 inline" />
              {i18nHome.hero.ctaSecondary||"了解评估详情"}
            </button>
          </div>

          {/* 核心功能预览卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16 px-4">
            {[
              {
                icon: Scale,
                color: "from-red-500 via-rose-500 to-pink-500",
                bgColor: "from-red-50/80 via-rose-50/60 to-pink-50/80",
                shadowColor: "shadow-red-500/25",
                glowColor: "from-red-400/30 to-pink-400/30",
                ringColor: "ring-red-200/50"
              },
              {
                icon: Apple,
                color: "from-emerald-500 via-green-500 to-teal-500",
                bgColor: "from-emerald-50/80 via-green-50/60 to-teal-50/80",
                shadowColor: "shadow-emerald-500/25",
                glowColor: "from-emerald-400/30 to-teal-400/30",
                ringColor: "ring-emerald-200/50"
              },
              {
                icon: Dumbbell,
                color: "from-blue-500 via-indigo-500 to-purple-500",
                bgColor: "from-blue-50/80 via-indigo-50/60 to-purple-50/80",
                shadowColor: "shadow-blue-500/25",
                glowColor: "from-blue-400/30 to-purple-400/30",
                ringColor: "ring-blue-200/50"
              }
            ].map((feature, index) => (
              <div key={index} className="group relative h-72 perspective-1000">
                {/* 背景光晕效果 */}
                <div className={`absolute inset-0 bg-gradient-to-br ${feature.glowColor} rounded-3xl opacity-0 group-hover:opacity-100 transition-all duration-500 blur-xl scale-105`}></div>

                {/* 主卡片容器 */}
                <div className={`relative bg-white/80 backdrop-blur-2xl border border-white/60 rounded-3xl p-8 ${feature.shadowColor} shadow-2xl hover:shadow-3xl transition-all duration-500 hover:-translate-y-4 hover:rotate-y-2 h-full flex flex-col group-hover:bg-white/90 ring-1 ${feature.ringColor} group-hover:ring-2 group-hover:ring-white/80`}>

                  {/* 顶部装饰线 */}
                  <div className={`absolute top-0 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-gradient-to-r ${feature.color} rounded-b-full opacity-60 group-hover:opacity-100 group-hover:w-24 transition-all duration-300`}></div>

                  {/* 图标容器 - 更精美的设计 */}
                  <div className="relative mb-8 mx-auto">
                    <div className={`w-20 h-20 bg-gradient-to-br ${feature.color} rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 relative overflow-hidden`}>
                      {/* 图标内部光效 */}
                      <div className="absolute inset-0 bg-gradient-to-tr from-white/20 to-transparent rounded-2xl"></div>
                      <div className="absolute inset-0 bg-gradient-to-bl from-transparent to-black/10 rounded-2xl"></div>
                      <feature.icon className="w-10 h-10 text-white relative z-10 drop-shadow-sm" />

                      {/* 浮动粒子效果 */}
                      <div className="absolute -top-1 -right-1 w-3 h-3 bg-white/40 rounded-full opacity-0 group-hover:opacity-100 group-hover:animate-ping transition-all duration-300"></div>
                    </div>

                    {/* 图标底部阴影 */}
                    <div className={`absolute top-2 left-1/2 transform -translate-x-1/2 w-20 h-20 bg-gradient-to-br ${feature.color} rounded-2xl opacity-20 blur-md group-hover:opacity-30 transition-opacity duration-300`}></div>
                  </div>

                  {/* 标题 */}
                  <h3 className="text-2xl font-bold text-slate-800 mb-4 text-center group-hover:text-slate-900 transition-colors duration-300 relative">
                    {i18nHome.hero.features[index].title}
                    <div className={`absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-gradient-to-r ${feature.color} group-hover:w-full transition-all duration-300 rounded-full`}></div>
                  </h3>

                  {/* 描述文字 */}
                  <p className="text-slate-600 leading-relaxed flex-1 text-center group-hover:text-slate-700 transition-colors duration-300 text-sm">
                    {i18nHome.hero.features[index].description}
                  </p>

                  {/* 底部装饰 */}
                  <div className="flex justify-center mt-6">
                    <div className={`w-8 h-8 bg-gradient-to-br ${feature.bgColor} rounded-full flex items-center justify-center opacity-60 group-hover:opacity-100 group-hover:scale-110 transition-all duration-300`}>
                      <div className={`w-3 h-3 bg-gradient-to-br ${feature.color} rounded-full`}></div>
                    </div>
                  </div>
                </div>

                {/* 卡片边缘光效 */}
                <div className={`absolute inset-0 rounded-3xl bg-gradient-to-r ${feature.color} opacity-0 group-hover:opacity-20 transition-opacity duration-500 pointer-events-none`} style={{padding: '1px'}}>
                  <div className="w-full h-full bg-transparent rounded-3xl"></div>
                </div>
              </div>
            ))}
          </div>

          </div>
        </section>

      {/* 主要功能区域 */}
      <section id="health-assessment" className="py-16 px-4 bg-gradient-to-b from-transparent to-white/50">
       
      </section>

      {/* 案例区域 */}
      <section className="py-20 px-4 bg-gradient-to-br from-white via-emerald-50/30 to-blue-50/30 relative overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-10 left-20 w-40 h-40 bg-gradient-to-br from-emerald-400/30 to-blue-400/30 rounded-full blur-xl"></div>
          <div className="absolute bottom-10 right-20 w-32 h-32 bg-gradient-to-br from-blue-400/30 to-purple-400/30 rounded-full blur-xl"></div>
          <div className="absolute top-1/3 right-1/3 w-28 h-28 bg-gradient-to-br from-purple-400/30 to-pink-400/30 rounded-full blur-xl"></div>
        </div>

        <div className="max-w-7xl mx-auto relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-emerald-600 via-blue-600 to-purple-600 bg-clip-text text-transparent mb-8">
              {i18nHome.cases.title}
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
              {i18nHome.cases.subtitle}
            </p>
            <div className="w-24 h-1 bg-gradient-to-r from-emerald-600 to-blue-600 mx-auto rounded-full mt-6"></div>
          </div>

          {/* 案例展示 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {i18nHome.cases.examples.map((caseItem, index) => (
              <div key={index} className="group relative">
                <div className="bg-white/90 backdrop-blur-xl border border-white/60 rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 hover:scale-105 h-full">
                  {/* 案例图标 */}
                  <div className="w-16 h-16 bg-gradient-to-br from-emerald-100 to-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-all duration-300">
                    <div className="text-3xl">{caseItem.icon}</div>
                  </div>

                  {/* 案例标题 */}
                  <h3 className="text-xl font-bold text-slate-800 mb-4 text-center group-hover:text-emerald-700 transition-colors">
                    {caseItem.title}
                  </h3>

                  {/* 案例描述 */}
                  <p className="text-slate-600 text-center leading-relaxed mb-6">
                    {caseItem.description}
                  </p>

                  {/* 案例结果 */}
                  <div className="bg-gradient-to-r from-emerald-50 to-blue-50 rounded-xl p-4 text-center">
                    <div className="text-2xl font-bold text-emerald-600 mb-1">
                      {caseItem.result}
                    </div>
                    <div className="text-sm text-slate-500">
                      {caseItem.resultLabel}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 行动按钮 */}
          <div className="text-center space-y-4">
            <p className="text-sm text-slate-500 max-w-2xl mx-auto">
              {i18nHome.cases.disclaimer}
            </p>
          </div>
        </div>
      </section>

      {/* 评估流程 */}
      <section className="py-20 px-4 bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/30 relative overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-xl"></div>
          <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-br from-emerald-400/20 to-blue-400/20 rounded-full blur-xl"></div>
          <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-xl"></div>
        </div>

        <div className="max-w-7xl mx-auto relative z-10">
          <div className="text-center mb-20">
            <h2 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-slate-800 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-8">
              {i18nHome.process.title}
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
              {i18nHome.process.subtitle}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 relative">
            {/* 高级连接线 */}
            <div className="hidden lg:block absolute top-1/2 left-0 right-0 h-px transform -translate-y-1/2 z-0">
              <div className="w-full h-full bg-gradient-to-r from-transparent via-blue-300/50 to-transparent"></div>
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-purple-300/30 to-transparent animate-pulse"></div>
            </div>

            {[
              { icon: User, gradient: 'from-blue-500 via-blue-600 to-indigo-600', bgGradient: 'from-blue-50 to-indigo-50', shadowColor: 'shadow-blue-500/20' },
              { icon: Scale, gradient: 'from-emerald-500 via-green-600 to-teal-600', bgGradient: 'from-emerald-50 to-teal-50', shadowColor: 'shadow-emerald-500/20' },
              { icon: Heart, gradient: 'from-purple-500 via-violet-600 to-purple-600', bgGradient: 'from-purple-50 to-violet-50', shadowColor: 'shadow-purple-500/20' },
              { icon: Brain, gradient: 'from-orange-500 via-amber-600 to-yellow-600', bgGradient: 'from-orange-50 to-amber-50', shadowColor: 'shadow-orange-500/20' }
            ].map((step, index) => (
              <div key={index} className="relative group h-80">
                <div className={`relative bg-white/90 backdrop-blur-xl border border-white/60 rounded-3xl p-8 text-center shadow-xl hover:shadow-2xl ${step.shadowColor} transition-all duration-500 hover:-translate-y-3 hover:scale-105 z-10 group-hover:bg-white/95 h-full flex flex-col`}>
                  {/* 步骤编号 - 更精美的设计 */}
                  <div className={`absolute -top-5 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-r ${step.gradient} text-white text-sm font-bold rounded-full flex items-center justify-center shadow-lg ring-4 ring-white/50 group-hover:ring-white/80 transition-all duration-300`}>
                    {i18nHome.process.steps[index].step}
                  </div>

                  {/* 图标容器 - 更大更精美 */}
                  <div className={`w-20 h-20 bg-gradient-to-br ${step.bgGradient} rounded-2xl flex items-center justify-center mx-auto mb-6 mt-4 shadow-inner group-hover:scale-110 transition-all duration-300 relative overflow-hidden`}>
                    <div className={`absolute inset-0 bg-gradient-to-r ${step.gradient} opacity-20 group-hover:opacity-30 transition-opacity duration-300`}></div>
                    <step.icon className="w-10 h-10 text-slate-700 relative z-10" />
                  </div>

                  {/* 标题 */}
                  <h3 className="text-xl font-bold text-slate-800 mb-4 group-hover:text-slate-900 transition-colors">
                    {i18nHome.process.steps[index].title}
                  </h3>

                  {/* 描述 */}
                  <p className="text-sm text-slate-600 leading-relaxed group-hover:text-slate-700 transition-colors flex-1">
                    {i18nHome.process.steps[index].desc}
                  </p>

                  {/* 底部装饰线 */}
                  <div className={`absolute bottom-0 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-gradient-to-r ${step.gradient} rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300`}></div>
                </div>

                {/* 卡片背景光晕效果 */}
                <div className={`absolute inset-0 bg-gradient-to-br ${step.gradient} rounded-3xl opacity-0 group-hover:opacity-10 transition-all duration-500 blur-xl -z-10`}></div>
              </div>
            ))}
          </div>

        </div>
      </section>

      {/* 核心优势 */}
      <section className="py-24 bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/30 relative overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))] -z-10"></div>
        <div className="absolute top-0 left-1/4 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
        <div className="absolute top-0 right-1/4 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
        <div className="absolute -bottom-8 left-1/3 w-72 h-72 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="text-center mb-20">
            <h2 className="text-5xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-6">
              {i18nHome.advantages.title}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              {i18nHome.advantages.subtitle}
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-10 mb-20">
            {i18nHome.advantages.features.map((feature, index) => (
              <div key={index} className="group relative min-h-[480px]">
                <div className={`absolute -inset-1 bg-gradient-to-r ${index === 0 ? "from-blue-600 to-purple-600" : "from-green-600 to-teal-600"} rounded-3xl blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200`}></div>
                <div className="relative bg-white rounded-3xl p-10 shadow-xl hover:shadow-2xl transition-all duration-300 border border-gray-100 h-full flex flex-col">
                  <div className="flex items-center mb-8">
                    <div className={`w-16 h-16 bg-gradient-to-br ${index === 0 ? "from-blue-500 to-blue-600" : "from-green-500 to-teal-600"} rounded-2xl flex items-center justify-center mr-6 shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                      {index === 0 ? <Brain className="w-8 h-8 text-white" /> : <Target className="w-8 h-8 text-white" />}
                    </div>
                    <div>
                      <h3 className="text-3xl font-bold text-gray-900 mb-2">{feature.title}</h3>
                      <p className={`${index === 0 ? "text-blue-600" : "text-green-600"} font-medium`}>{feature.subtitle}</p>
                    </div>
                  </div>
                  <p className="text-gray-600 mb-8 leading-relaxed text-lg">
                    {feature.description}
                  </p>
                  <div className="grid grid-cols-2 gap-4 mb-8">
                    {feature.tags.slice(0, 4).map((tag, tagIndex) => (
                      <div key={tagIndex} className="flex items-center text-gray-600">
                        <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                        <span>{tag}</span>
                      </div>
                    ))}
                  </div>
                  <div className="flex flex-wrap gap-3 mt-auto">
                    {feature.tags.map((tag, tagIndex) => (
                      <span key={tagIndex} className={`px-4 py-2 ${index === 0 ? (tagIndex === 0 ? "bg-gradient-to-r from-blue-100 to-blue-200 text-blue-700" : tagIndex === 1 ? "bg-gradient-to-r from-green-100 to-green-200 text-green-700" : "bg-gradient-to-r from-purple-100 to-purple-200 text-purple-700") : (tagIndex === 0 ? "bg-gradient-to-r from-green-100 to-green-200 text-green-700" : tagIndex === 1 ? "bg-gradient-to-r from-blue-100 to-blue-200 text-blue-700" : "bg-gradient-to-r from-orange-100 to-orange-200 text-orange-700")} text-sm font-medium rounded-full`}>
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 特色功能亮点 - 重新设计 */}
          <div className="grid md:grid-cols-3 gap-8">
            {[
              { icon: CheckCircle, gradient: "from-blue-500 to-blue-600" },
              { icon: Zap, gradient: "from-yellow-500 to-orange-500" },
              { icon: Users, gradient: "from-purple-500 to-pink-500" }
            ].map((feature, index) => (
              <div key={index} className="group text-center p-8 rounded-2xl bg-white/50 backdrop-blur-sm border border-white/20 hover:bg-white/80 transition-all duration-300 hover:shadow-lg">
                <div className={`w-20 h-20 bg-gradient-to-br ${feature.gradient} rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                  <feature.icon className="w-10 h-10 text-white" />
                </div>
                <h4 className="text-2xl font-bold text-gray-900 mb-4">{i18nHome.advantages.highlights[index].title}</h4>
                <p className="text-gray-600 leading-relaxed">{i18nHome.advantages.highlights[index].description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ部分 */}
      <section className="py-16 px-4 bg-gradient-to-b from-slate-50/50 to-white">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-slate-800 mb-6">{i18nHome.faq.title}</h2>
            <p className="text-xl text-slate-600">{i18nHome.faq.subtitle}</p>
          </div>

          <Accordion type="single" collapsible className="space-y-6">
            {i18nHome.faq.items.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index + 1}`} className="bg-white/80 backdrop-blur-xl border border-white/50 rounded-2xl px-6 shadow-lg">
                <AccordionTrigger className="text-left font-bold text-slate-800 hover:no-underline text-lg py-6">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="text-slate-600 leading-relaxed pb-6">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </section>
    </div>
  )
}