import { Sparkles } from "lucide-react"
import { Accordion, Accordion<PERSON>ontent, AccordionTrigger, AccordionItem } from "@/components/ui/accordion"

import { type Locale, getPathname, generateAlternates } from "@/i18n-config";
import { getDictionary, i18nNamespaces } from '@/i18n'
import { Home as HomeType } from "@/types/locales";

import { host } from '@/config/config'

export const dynamic = 'force-dynamic'

export async function generateMetadata({ params }: { params: { lang: Locale } }) {
  const { lang } = await params
  const alternates = generateAlternates(lang, '/');
  const i18nHome = await getDictionary<HomeType>(lang, i18nNamespaces.home);
  return {
    title: i18nHome.meta.title,
    description: i18nHome.meta.description,
    keywords: i18nHome.meta.keywords,
    twitter: {
      card: "summary_large_image", 
      title: i18nHome.meta.title,
      description: i18nHome.meta.description
    },
    openGraph: {
      type: "website",
      url: `${host}${getPathname(lang, '')}`,
      title: i18nHome.meta.title,
      description: i18nHome.meta.description,
      siteName: i18nHome.hero.title
    },
    alternates: {
      canonical: `${host}${getPathname(lang, '')}`,
      languages: alternates
    }
  }
}

export default async function Home({
  params,
}: {
  params: { lang: string }
}) {
  const { lang } = await params
  const i18nHome = await getDictionary<HomeType>(lang, i18nNamespaces.home);

  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20">
      {/* 翻译服务主题背景装饰 */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        {/* 简洁的波形背景 */}
        <div className="absolute top-0 left-0 w-full h-full opacity-[0.03]">
          <svg className="w-full h-full" viewBox="0 0 1200 600" preserveAspectRatio="none">
            <path d="M0,400 Q200,350 400,380 T800,360 Q1000,320 1200,340"
                  stroke="currentColor"
                  strokeWidth="2"
                  fill="none"
                  className="text-blue-600"/>
            <path d="M0,450 Q300,400 600,430 T1200,410"
                  stroke="currentColor"
                  strokeWidth="1"
                  fill="none"
                  className="text-purple-500 opacity-60"/>
          </svg>
        </div>

        {/* 简洁的装饰圆点 */}
        <div className="absolute top-20 left-10 w-12 h-12 bg-blue-100 rounded-full opacity-20"></div>
        <div className="absolute top-32 right-16 w-8 h-8 bg-purple-100 rounded-full opacity-20"></div>
        <div className="absolute bottom-32 right-20 w-16 h-16 bg-indigo-100 rounded-full opacity-20"></div>
        <div className="absolute bottom-64 left-12 w-10 h-10 bg-cyan-100 rounded-full opacity-20"></div>
      </div>

      {/* Hero区域 - 重新设计 */}
      <section className="flex items-center justify-center relative px-4 py-16 pt-4">
        <div className="max-w-7xl mx-auto text-center space-y-8 w-full">
          {/* AI智能标签 - 全新高级设计 */}
          <div className="relative group">
            {/* 背景光晕效果 */}
            <div className="absolute inset-0 bg-gradient-to-r from-violet-600/20 via-blue-600/20 to-cyan-600/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-500 scale-110"></div>

            {/* 主标签容器 */}
            <div className="relative inline-flex items-center bg-white/90 backdrop-blur-2xl border-2 border-white/50 px-8 py-4 rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-500 hover:-translate-y-1 overflow-hidden">
              {/* 内部渐变背景 */}
              <div className="absolute inset-0 bg-gradient-to-r from-violet-50/80 via-blue-50/60 to-cyan-50/80 opacity-60"></div>

              {/* 动态边框光效 */}
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-violet-500 via-blue-500 to-cyan-500 opacity-0 group-hover:opacity-30 transition-opacity duration-500" style={{padding: '1px'}}>
                <div className="w-full h-full bg-transparent rounded-2xl"></div>
              </div>

              {/* 图标和文字 */}
              <div className="relative flex items-center">
                <div className="relative mr-4">
                  <Sparkles className="w-6 h-6 text-violet-600 animate-pulse relative z-10" />
                  <div className="absolute inset-0 w-6 h-6 bg-violet-400 rounded-full opacity-20 animate-ping"></div>
                </div>
                <span className="text-lg font-bold bg-gradient-to-r from-violet-700 via-blue-700 to-cyan-700 bg-clip-text text-transparent tracking-wide">
                  {i18nHome.hero.aiLabel}
                </span>
              </div>

              {/* 装饰性粒子 */}
              <div className="absolute top-1 right-2 w-2 h-2 bg-gradient-to-r from-violet-400 to-blue-400 rounded-full opacity-60 animate-bounce"></div>
              <div className="absolute bottom-1 left-3 w-1.5 h-1.5 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full opacity-40 animate-pulse"></div>
            </div>
          </div>

          {/* 主标题 - 全新设计 */}
          <div className="space-y-8 relative">
            {/* 背景装饰元素 */}
            <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 w-32 h-32 bg-gradient-to-r from-violet-200/30 to-cyan-200/30 rounded-full blur-3xl opacity-50"></div>

            <div className="relative">
              <h1 className="text-5xl md:text-7xl font-black leading-tight tracking-tight">
                {/* 主标题 - 立体渐变效果 */}
                <span className="block relative">
                  <span className="absolute inset-0 bg-gradient-to-r from-slate-400 via-slate-300 to-slate-400 bg-clip-text text-transparent blur-sm opacity-40">
                    {i18nHome.hero.title}
                  </span>
                  <span className="relative bg-gradient-to-r from-slate-700 via-violet-600 to-blue-600 bg-clip-text text-transparent break-keep">
                    {i18nHome.hero.title}
                  </span>

                  {/* 标题装饰线 */}
                  <div className="absolute -bottom-4 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-violet-400/60 to-transparent rounded-full"></div>
                </span>

                {/* 副标题 - 现代排版 */}
                <span className="block text-lg md:text-2xl mt-8 font-medium relative">
                  <span className="inline-flex flex-wrap items-center justify-center gap-4 gap-y-6 text-slate-600">
                    {i18nHome.hero.subtitle.split(' · ').map((item, index) => (
                      <span key={index} className="relative">
                        <span className={`px-4 py-2 bg-gradient-to-r ${
                          index === 0 ? 'from-violet-100 to-blue-100 border-violet-200/50' :
                          index === 1 ? 'from-blue-100 to-cyan-100 border-blue-200/50' :
                          'from-cyan-100 to-emerald-100 border-cyan-200/50'
                        } rounded-xl border shadow-sm transition-all duration-300 hover:shadow-md hover:scale-105`}>
                          {item.trim()}
                        </span>
                        {index < 2 && (
                          <span className={`absolute -right-6 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-gradient-to-r ${
                            index === 0 ? 'from-violet-400 to-blue-400' : 'from-blue-400 to-cyan-400'
                          } rounded-full animate-pulse`}></span>
                        )}
                      </span>
                    ))}
                  </span>
                </span>
              </h1>
            </div>

            {/* 描述文字 - 优雅设计 */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/50 to-transparent rounded-2xl blur-sm"></div>
              <p className="relative text-lg md:text-xl text-slate-600 max-w-5xl mx-auto leading-relaxed bg-white/60 backdrop-blur-sm rounded-2xl px-8 py-6 border border-white/30 shadow-lg">
                {i18nHome.hero.description.split('\n').map((line, index) => (
                  <span key={index} className="block">
                    {line}
                    {index === 0 && <br className="hidden md:block" />}
                  </span>
                ))}
              </p>
            </div>
          </div>

        </div>
      </section>

      {/* 案例区域 */}
      <section className="py-20 px-4 bg-gradient-to-br from-white via-emerald-50/30 to-blue-50/30 relative overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-10 left-20 w-40 h-40 bg-gradient-to-br from-emerald-400/30 to-blue-400/30 rounded-full blur-xl"></div>
          <div className="absolute bottom-10 right-20 w-32 h-32 bg-gradient-to-br from-blue-400/30 to-purple-400/30 rounded-full blur-xl"></div>
          <div className="absolute top-1/3 right-1/3 w-28 h-28 bg-gradient-to-br from-purple-400/30 to-pink-400/30 rounded-full blur-xl"></div>
        </div>

        <div className="max-w-7xl mx-auto relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-emerald-600 via-blue-600 to-purple-600 bg-clip-text text-transparent mb-8">
              {i18nHome.cases.title}
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
              {i18nHome.cases.subtitle}
            </p>
            <div className="w-24 h-1 bg-gradient-to-r from-emerald-600 to-blue-600 mx-auto rounded-full mt-6"></div>
          </div>

          {/* 案例展示 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {i18nHome.cases.examples.map((caseItem, index) => (
              <div key={index} className="group relative">
                <div className="bg-white/90 backdrop-blur-xl border border-white/60 rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 hover:scale-105 h-full">
                  {/* 案例图标 */}
                  <div className="w-16 h-16 bg-gradient-to-br from-emerald-100 to-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-all duration-300">
                    <div className="text-3xl">{caseItem.icon}</div>
                  </div>

                  {/* 案例标题 */}
                  <h3 className="text-xl font-bold text-slate-800 mb-4 text-center group-hover:text-emerald-700 transition-colors">
                    {caseItem.title}
                  </h3>

                  {/* 案例描述 */}
                  <p className="text-slate-600 text-center leading-relaxed mb-6">
                    {caseItem.description}
                  </p>

                  {/* 案例结果 */}
                  <div className="bg-gradient-to-r from-emerald-50 to-blue-50 rounded-xl p-4 text-center">
                    <div className="text-2xl font-bold text-emerald-600 mb-1">
                      {caseItem.result}
                    </div>
                    <div className="text-sm text-slate-500">
                      {caseItem.resultLabel}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 行动按钮 */}
          <div className="text-center space-y-4">
            <p className="text-sm text-slate-500 max-w-2xl mx-auto">
              {i18nHome.cases.disclaimer}
            </p>
          </div>
        </div>
      </section>

      {/* FAQ部分 */}
      <section className="py-16 px-4 bg-gradient-to-b from-slate-50/50 to-white">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-slate-800 mb-6">{i18nHome.faq.title}</h2>
            <p className="text-xl text-slate-600">{i18nHome.faq.subtitle}</p>
          </div>

          <Accordion type="single" collapsible className="space-y-6">
            {i18nHome.faq.items.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index + 1}`} className="bg-white/80 backdrop-blur-xl border border-white/50 rounded-2xl px-6 shadow-lg">
                <AccordionTrigger className="text-left font-bold text-slate-800 hover:no-underline text-lg py-6">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="text-slate-600 leading-relaxed pb-6">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </section>
    </div>
  )
}