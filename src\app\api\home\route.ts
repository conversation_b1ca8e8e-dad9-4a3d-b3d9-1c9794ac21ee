import { NextResponse } from 'next/server';

import { addNamingTask } from '@/actions/user-task';

import { updateUserCreditByClerkId, addUserCredit } from '@/actions/credits';



async function executeTaskAndManageCredits(
  
  userId: string,
  action: string,
  params: any,
  result: any,
  ip: string,
  credits: number
) {
  // Add task record
  await addNamingTask({
    userId,
    action,
    params,
    result,
    ip
  });

  // Update user credits (consume 1 credit)
  const creditCost = 10;
  await updateUserCreditByClerkId(creditCost, userId, credits);
  // Add credit consumption record
  await addUserCredit({
    user_id: userId,
    credit_amount: creditCost,
    credit_type: '',
    credit_transaction_type: '0', // Consumption type
    credit_desc: `Used ${creditCost} credit for ${action}`,
    order_price: 0
  });

  return NextResponse.json(result, { status: 200 });
}



