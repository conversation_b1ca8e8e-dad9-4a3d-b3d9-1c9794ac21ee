export interface Order {
    orderId: string;
    fullName: string;
    email: string;
    product: string;
    amount: number;
    type: string;
    status: string;
    date: string;
}


export interface UserOrder {
    orderId: string;
    credits: string;
    price: string;
    date: string;
    type: string;
    status: string;
}

export interface UserSubscription {
    orderId: string;
    credits: string;
    subscriptionType: string; // 'monthly' | 'quarterly' | 'annual'
    price: string;
    date: string;
    renewalDate: string;
    isActive: boolean;
    status: 'active' | 'cancelled';
    // 累积订阅相关字段（可选，仅最新记录有）
    isCumulative?: boolean;
    totalSubscriptions?: number;
    cumulativeExpiryDate?: string;
}