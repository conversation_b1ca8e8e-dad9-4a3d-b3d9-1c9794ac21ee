import React, { useCallback, useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import { useSession } from 'next-auth/react';
import { useRouter, useParams } from 'next/navigation';
import { findUserCreditsByUserId } from "@/actions/user";
import { getDictionary, i18nNamespaces } from '@/i18n';
import type { Pricing } from '@/types/locales/pricing';

import {
  EmbeddedCheckoutProvider,
  EmbeddedCheckout
} from '@stripe/react-stripe-js';


interface CheckoutProps{
  priceId:string;
  mode:string;
  className?: string;
}
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

export default function CheckoutForm({priceId,mode,className}:CheckoutProps) {

  const { data: session } = useSession();
  const router = useRouter();
  const params = useParams();
  const lang = params.lang as string;
  const [isReady, setIsReady] = useState(false);
  const [isUpdatingCredits, setIsUpdatingCredits] = useState(false);
  const [currentCredits, setCurrentCredits] = useState<number>(0);
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [pricingI18n, setPricingI18n] = useState<Pricing | null>(null);

  // 加载翻译
  useEffect(() => {
    const loadPricingI18n = async () => {
      try {
        const data = await getDictionary<Pricing>(lang, i18nNamespaces.pricing);
        setPricingI18n(data);
      } catch (error) {
        console.error('Failed to load pricing i18n:', error);
      }
    };
    loadPricingI18n();
  }, [lang]);

  // 等待用户认证状态完全加载
  useEffect(() => {
    if (session?.user?.id && session?.user?.email) {
      setIsReady(true);
      // 初始加载积分
      loadCredits();
    } else {
      setIsReady(false);
    }
  }, [session]);

  const loadCredits = async () => {
    if (session?.user?.id) {
      const credits = await findUserCreditsByUserId(session.user.id, true);
      setCurrentCredits(credits);
    }
  };

  async function handlePaymentSuccess(){
    setIsUpdatingCredits(true);

    try {
      // 延迟重试机制，确保 webhook 有时间处理
      const maxRetries = 3;
      const initialDelay = 3000; // 3秒初始延迟

      for (let i = 0; i < maxRetries; i++) {
        try {
          // 延迟等待（逐渐增加延迟时间）
          const delay = initialDelay + (i * 2000); // 3s, 5s, 7s
          await new Promise(resolve => setTimeout(resolve, delay));

          // 获取最新积分
          const credits = await findUserCreditsByUserId(session?.user?.id, true);

          // 如果积分大于原来的值，说明 webhook 已经处理完成
          if (credits > currentCredits) {
            await updateCredits();
            break;
          }
        } catch (error) {
          console.error(`第 ${i + 1} 次尝试失败:`, error);
        }

        // 最后一次尝试
        if (i === maxRetries - 1) {
          await updateCredits();
        }
      }

      // 支付成功后，等待2秒然后跳转到首页健康评估区域
      setIsRedirecting(true);

      setTimeout(() => {
        router.push(`/${lang}#health-assessment`);
      }, 2000);

    } finally {
      setIsUpdatingCredits(false);
    }
  }

  const fetchClientSecret = useCallback(() => {
    if (!session?.user?.id) {
      return Promise.reject(new Error('用户ID不存在，请先登录'));
    }

    if (!session?.user?.email) {
      return Promise.reject(new Error('用户邮箱不存在'));
    }
    // Create a Checkout Session
    return fetch("/api/stripe", {
      method: "POST",
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        priceId: priceId,
        userId: session.user.id,
        type: mode === "payment" ? "1" : "2",
        customerEmail: session.user.email,
        lang: lang
      }),
    })
      .then(async (res) => {
        const data = await res.json().catch(() => ({}));

        if (data.error) {
          // 如果是重复订阅错误，显示提示并刷新页面，不抛出错误
          if (data.code === 'ACTIVE_SUBSCRIPTION_EXISTS' || res.status === 400) {
            const fallbackMessage = pricingI18n?.errors?.activeSubscriptionExists ||
              (lang === 'zh' ? '您已有活跃的订阅计划，无法重复订阅。' : 'You already have an active subscription.');
            alert(data.error || fallbackMessage);
            window.location.reload();
            return null;
          }
          throw new Error(data.error);
        }

        if (!res.ok) {
          throw new Error(`请求失败: ${res.status}`);
        }

        return data.clientSecret;
      })
      .catch((error) => {
        console.error('创建 Stripe 会话失败:', error);
        // 如果错误信息包含重复订阅相关内容，不再抛出
        if (error.message && error.message.includes('重复订阅')) {
          return null;
        }
        throw error;
      });
  }, [session?.user?.id, session?.user?.email, priceId, mode, isReady]);

  const options = {
    fetchClientSecret,
    onComplete: handlePaymentSuccess
  };

  async function updateCredits() {
    const credits = await findUserCreditsByUserId(session?.user?.id, true); // 强制刷新
    setCurrentCredits(credits);
    
    // 触发全局状态刷新
    window.dispatchEvent(new CustomEvent('creditsUpdated', { detail: { credits } }));
  }

  // 如果用户认证状态未就绪，显示加载状态
  if (!isReady) {
    return (
      <div className="w-full h-[600px] md:h-full md:min-w-[992px] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">正在加载支付信息...</p>
          <p className="text-sm text-muted-foreground mt-2">
            用户ID: {session?.user?.id || '加载中...'}
          </p>
          <p className="text-sm text-muted-foreground">
            邮箱: {session?.user?.email || '加载中...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
    <EmbeddedCheckoutProvider
      stripe={stripePromise}
      options={options}
    >
      <EmbeddedCheckout className={`w-full h-[600px] md:h-full md:min-w-[992px] overflow-auto`}/>
    </EmbeddedCheckoutProvider>
      
      {/* 积分更新状态提示 */}
      {isUpdatingCredits && !isRedirecting && (
        <div className="fixed top-4 right-4 bg-primary text-primary-foreground px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-foreground"></div>
          <span className="text-sm">正在订阅...</span>
        </div>
      )}

      {/* 跳转状态提示 */}
      {isRedirecting && (
        <div className="fixed top-4 right-4 bg-green-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          <span className="text-sm">订阅成功！正在跳转到健康评估...</span>
        </div>
      )}
    </>
  )
}