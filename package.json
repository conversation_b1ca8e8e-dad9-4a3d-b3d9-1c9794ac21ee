{"name": "slimmerplan-health-assessment", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@airwallex/components-sdk": "^1.18.0", "@ffmpeg/util": "^0.12.2", "@hookform/resolvers": "^4.1.1", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@neondatabase/serverless": "^0.10.4", "@next/third-parties": "^15.2.2", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@stripe/react-stripe-js": "^3.4.0", "@stripe/stripe-js": "^6.0.0", "@upstash/redis": "^1.34.4", "@vercel/postgres": "^0.10.0", "autoprefixer": "^10.4.20", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "embla-carousel-react": "^8.5.2", "ffmpeg-wasm": "^1.0.1", "framer-motion": "^12.23.3", "html2canvas": "^1.4.1", "input-otp": "^1.4.2", "ioredis": "^5.6.0", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.475.0", "nanoid": "^5.1.5", "next": "^15.3.5", "next-auth": "^4.24.11", "next-themes": "^0.4.4", "openai": "^4.86.1", "pg": "^8.13.3", "react": "^19.0.0", "react-day-picker": "^9.5.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.1", "sonner": "^2.0.1", "stripe": "^17.7.0", "svix": "^1.59.2", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "undici": "^7.11.0", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/mdx": "^15.3.5", "@tailwindcss/typography": "^0.5.16", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/lodash": "^4.17.20", "@types/mdx": "^2.0.13", "@types/node": "^20.19.4", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "^15.3.5", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}