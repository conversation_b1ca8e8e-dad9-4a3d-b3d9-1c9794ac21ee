{"meta": {"title": "Évaluation de la santé de l'slimmerplan - Menu diététique de l'slimmerplan | Guide d'exercices pour l'slimmerplan & Plan alimentaire pour la tension artérielle", "description": "Grâce à des questionnaires de santé d<PERSON>, utilisez la technologie IA pour l'évaluation des risques d'slimmerplan afin d'obtenir des plans de contrôle de la tension artérielle personnalisés, des recommandations diététiques, des conseils d'exercice et des suggestions d'ajustement du mode de vie.", "alt": "Évaluation de la santé de l'slimmerplan - Système d'analyse intelligente des risques de tension artérielle", "keywords": "régime DASH,aliments pour l'slimmerplan,alimentation saine pour l'slimmerplan"}, "hero": {"aiLabel": "Évaluation minceur par IA", "title": "Évaluation Min<PERSON>ur <PERSON>", "subtitle": "Professionnel · Intelligent · Solution personnalisée", "description": "Basé sur les standards nutritionnels internationaux et l'analyse intelligente par IA\nPlan de gestion du poids sur mesure pour protéger votre santé corporelle", "ctaPrimary": "Commencer l'évaluation minceur", "ctaSecondary": "Détails de l'évaluation", "features": [{"title": "Évaluation graisse corporelle", "description": "Évaluation IMC et taux de graisse selon les standards scientifiques"}, {"title": "Solution personnalisée", "description": "Plan de contrôle minceur santé adapté"}, {"title": "Suivi continu", "description": "Surveillance à long terme du poids avec ajustements dynamiques"}]}, "pyramid": {"title": "Pyramide de gestion tensionnelle", "subtitle": "Structure hiérarchique scientifique pour une protection cardiovasculaire complète", "managementLevels": "Niveaux de gestion", "fromAcuteToPrevention": "De l'aigu à la prévention", "healthScore": "85 points", "healthScoreLabel": "Score de santé", "levels": [{"level": 1, "label": "Phase aiguë", "percentage": "80%"}, {"level": 2, "label": "Médicaments", "percentage": "60%"}, {"level": 3, "label": "Mode de vie", "percentage": "40%"}, {"level": 4, "label": "Surveillance", "percentage": "20%"}, {"level": 5, "label": "Prévention", "percentage": "5%"}], "pyramidLevels": [{"title": "Gestion aiguë", "subtitle": "Contrôle urgent et évaluation des risques", "level": "Niveau 1", "category": "Intervention d'urgence", "systolic": "≥180", "diastolic": "≥110", "intervention": "Immédiate", "systolicUnit": "Pression systolique mmHg", "diastolicUnit": "Pression diastolique mmHg", "interventionUnit": "Intervention médicale"}, {"title": "Traitement médicament<PERSON>", "subtitle": "Protocole standardisé d'antihypertenseurs", "level": "Niveau 2", "category": "Intervention médicale", "systolic": "160-179", "diastolic": "100-109", "intervention": "4-6 semaines", "systolicUnit": "Pression systolique mmHg", "diastolicUnit": "Pression diastolique mmHg", "interventionUnit": "Période d'efficacité"}, {"title": "Intervention sur le mode de vie", "subtitle": "Ajustements alimentaires et comportementaux", "level": "Niveau 3", "category": "Amélioration comportementale", "systolic": "140-159", "diastolic": "90-99", "intervention": "3-6 mois", "systolicUnit": "Pression systolique mmHg", "diastolicUnit": "Pression diastolique mmHg", "interventionUnit": "Période d'amélioration"}, {"title": "Surveillance santé", "subtitle": "Examens réguliers et suivi des données", "level": "Niveau 4", "category": "Prévention et surveillance", "systolic": "130-139", "diastolic": "85-89", "intervention": "<PERSON><PERSON><PERSON>", "systolicUnit": "Pression systolique mmHg", "diastolicUnit": "Pression diastolique mmHg", "interventionUnit": "Fréquence de surveillance"}, {"title": "Prévention santé", "subtitle": "Éducation sanitaire et prévention des risques", "level": "Niveau 5", "category": "Prévention de base", "systolic": "<130", "diastolic": "<85", "intervention": "<PERSON><PERSON><PERSON>", "systolicUnit": "Pression systolique mmHg", "diastolicUnit": "Pression diastolique mmHg", "interventionUnit": "Fréquence des bilans"}], "description": "Notre système d'évaluation IA élaborera pour vous un plan de gestion scientifique multi-niveaux basé sur votre tension artérielle et votre état de santé", "stats": [{"number": "5 niveaux", "label": "Niveaux de gestion"}, {"number": "360°", "label": "Couverture complète"}, {"number": "<PERSON><PERSON><PERSON><PERSON>", "label": "Solution sur mesure"}, {"number": "IA intelligente", "label": "Analyse scientifique"}]}, "statistics": {"stats": [{"number": "50k+", "label": "Utilisateurs"}, {"number": "98%", "label": "Précision"}, {"number": "30 jours", "label": "Plan personnalisé"}, {"number": "Professionnel", "label": "Standards médicaux"}]}, "assessment": {"title": "Commencez votre évaluation", "subtitle": "Obtenez en quelques minutes un rapport professionnel d'évaluation de gestion du poids et un plan de perte de poids saine personnalisé"}, "cases": {"title": "Histoires de Réussite", "subtitle": "Parcours réels de perte de poids saine, solutions scientifiques aidant à atteindre les objectifs de santé", "examples": [{"icon": "👩‍💼", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Travail sédentaire, 15kg de surpoids, a perdu du poids avec succès et maintenu une forme corporelle saine", "result": "-12kg", "resultLabel": "Perte en 3 mois"}, {"icon": "👨‍🍳", "title": "Chef <PERSON>", "description": "Habitudes alimentaires irrégulières dues à la profession, pourcentage élevé de graisse corporelle, amélioré grâce à la nutrition scientifique", "result": "-8%", "resultLabel": "Ré<PERSON> de graisse"}, {"icon": "👩‍🎓", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Pression d'études élevée et horaires irréguliers causant des fluctuations de poids, a retrouvé l'équilibre corporel", "result": "95%", "resultLabel": "Indicateurs améliorés"}], "disclaimer": "* Les cas ci-dessus sont tous des retours d'utilisateurs réels. Les résultats spécifiques peuvent varier selon l'individu."}, "process": {"title": "Processus d'évaluation intelligente", "subtitle": "4 étapes simples pour une évaluation médicale professionnelle de perte de poids", "steps": [{"title": "Informations de base", "desc": "Renseignez vos informations santé de base (âge, sexe, taille, poids)", "step": "01"}, {"title": "Données de poids", "desc": "Enregistrez vos mesures actuelles et récentes de poids", "step": "02"}, {"title": "État de santé", "desc": "Détaillez vos antécédents médicaux et traitements", "step": "03"}, {"title": "Analyse IA", "desc": "Génération d'une évaluation de perte de poids et de conseils personnalisés", "step": "04"}]}, "advantages": {"title": "Pourquoi nous choisir", "subtitle": "Intégrant la technologie IA de pointe avec les standards nutritionnels internationaux pour fournir des solutions de perte de poids personnalisées, scientifiques, sûres et efficaces", "features": [{"title": "Analyse IA intelligente", "subtitle": "Moteur intelligent de gestion du poids basé sur la science nutritionnelle", "description": "Utilisant des algorithmes avancés de gestion du poids internationaux, intégrant les dernières découvertes de recherche nutritionnelle, l'IA analyse intelligemment vos données corporelles, habitudes alimentaires et mode de vie pour fournir une évaluation précise de la graisse corporelle et des recommandations de perte de poids.", "tags": ["<PERSON><PERSON><PERSON> grai<PERSON> co<PERSON>", "Évaluation nutritionnelle", "Calcul métabolisme", "Recommandations intelligentes"]}, {"title": "Plan de perte de poids personnalisé", "subtitle": "Stratégies scientifiques de réduction de poids sur mesure", "description": "Basé sur vos caractéristiques corporelles, poids cible, préférences alimentaires et capacité d'exercice, génère intelligemment des plans de perte de poids exclusifs, incluant planification de repas personnalisée, programmes d'exercice, optimisation du sommeil et conseils complets.", "tags": ["<PERSON><PERSON><PERSON><PERSON>", "Plan d'exercice", "Optimisation sommeil", "<PERSON><PERSON><PERSON>"]}], "highlights": [{"title": "Scientifique et sûr", "description": "Basé sur les standards nutritionnels internationaux et données de recherche clinique, garantissant un processus de perte de poids sain et évitant la réduction de poids extrême"}, {"title": "Rapide et pratique", "description": "Complétez l'évaluation du poids en seulement 3-5 minutes, recevez instantanément un plan de perte de poids personnalisé et des conseils d'exécution détaillés"}, {"title": "<PERSON><PERSON><PERSON>", "description": "Créé par des nutritionnistes seniors, experts en fitness et équipe technologique IA, optimisant continuellement les algorithmes de perte de poids"}]}, "faq": {"title": "Questions fréquentes", "subtitle": "Réponses aux questions courantes sur l'évaluation de perte de poids saine", "items": [{"question": "Quelle est la précision de cette évaluation ?", "answer": "Notre évaluation repose sur les normes nutritionnelles internationales et les directives de gestion du poids combinées à une analyse IA, offrant une grande valeur indicative. Avec une précision supérieure à 98%, elle ne remplace pas un diagnostic de nutritionniste professionnel mais constitue une référence utile pour discuter avec votre nutritionniste ou médecin."}, {"question": "Mes données personnelles sont-elles sécurisées ?", "answer": "Nous protégeons strictement votre vie privée. Toutes les données santé sont chiffrées de bout en bout selon les standards internationaux de protection, utilisées uniquement pour votre rapport d'évaluation et jamais partagées avec des tiers ou à des fins commerciales."}, {"question": "Que contient le rapport d'évaluation ?", "answer": "Le rapport détaillé inclut : (1) Évaluation et interprétation de l'indice de masse corporelle et du pourcentage de graisse corporelle (2) Analyse complète de votre santé (3) Plan personnalisé de contrôle de perte de poids (4) Conseils nutritionnels et sportifs scientifiques (5) Recommandations d'amélioration du mode de vie (6) Plan de suivi et surveillance des progrès."}, {"question": "Quand refaire une évaluation ?", "answer": "Nous recommandons des réévaluations régulières selon vos progrès de perte de poids. Durant des ajustements actifs du mode de vie et des habitudes alimentaires, évaluez mensuellement pour suivre les progrès. Si votre poids est stable, une évaluation trimestrielle ou semestrielle suffit pour vérifier l'efficacité du plan."}]}, "form": {"mainTitle": "Plan Alimentaire Personnalisé pour l'Slimmerplan", "subtitle": "Nous avons besoin du soutien de vos données de santé ! Remplissez simplement le questionnaire (environ 2 minutes), et nous pourrons vous fournir des recommandations précises de recettes faibles en sodium et riches en potassium pour vous aider à contrôler votre tension artérielle de manière scientifique.", "step": "Étape", "ageLabel": "Âge", "years": "ans", "prevButton": "Précédent", "nextButton": "Suivant", "submitButton": "Commencer la Personnalisation", "submittingButton": "Évaluation en cours...", "loadExistingTitle": "Évaluation précédente trouvée", "loadExistingDesc": "Nous avons trouvé un enregistrement récent d'évaluation de santé, voulez-vous le charger ?", "loadExistingAction": "Charger l'enregistrement", "loadExistingCancel": "Remplir à nouveau", "submitSuccessTitle": "Évaluation réussie", "submitSuccessDesc": "Votre rapport de santé a été généré.", "submitErrorAuthTitle": "Veuillez d'abord vous connecter", "submitErrorAuthDesc": "Veuillez vous connecter avant de procéder à votre évaluation de santé.", "submitErrorNetworkTitle": "Évaluation échouée", "submitErrorNetworkDesc": "Le serveur a des problèmes, veuillez réessayer plus tard.", "questions": {"basicInfoTitle": "Informations de base", "birthDate": {"title": "Quelle est votre date de naissance ?", "monthNames": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Mars", "Avril", "<PERSON>", "Juin", "<PERSON><PERSON><PERSON>", "Août", "Septembre", "Octobre", "Novembre", "Décembre"], "daySuffix": ""}, "gender": {"title": "Quel est votre sexe ?", "male": "<PERSON><PERSON>", "female": "<PERSON>mme", "non_binary": "Non-binaire"}, "height": {"title": "Quelle est votre taille ?", "unit": "cm"}, "weight": {"title": "Quel est votre poids ?", "unit": "kg", "label": "Poids", "range": "Plage : {min} - {max} kg"}, "bloodPressure": {"title": "Quelle est votre tension artérielle actuelle ?", "description": "Faites glisser pour ajuster la pression systolique et diastolique", "systolicLabel": "Pression systolique", "diastolicLabel": "Pression diastolique", "unknownBloodPressure": "Vous ne connaissez pas votre tension artérielle ?", "normalBloodPressure": "Tension Artérielle Normale", "highBloodPressure": "Tension Artérielle Élevée", "lowBloodPressure": "Tension Artérielle Basse"}, "medicalHistory": {"title": "Avez-vous des problèmes de santé ?", "familyHistory": "Antécédents familiaux d'slimmerplan", "diabetes": "Diabè<PERSON>", "heartDisease": "Maladie cardiaque", "kidneyDisease": "Maladie <PERSON>", "stroke": "AVC", "cholesterol": "<PERSON><PERSON><PERSON><PERSON>"}, "exercise": {"title": "Combien d'heures d'exercice faites-vous par semaine en moyenne ?", "none": "Pas d'exercice", "light": "1-2 heures/semaine", "moderate": "3-4 heures/semaine", "intense": "5+ heures/semaine"}, "salt": {"title": "Comment est votre consommation quotidienne de sel ?", "low": "<PERSON><PERSON>", "normal": "Modérément sa<PERSON>", "high": "<PERSON><PERSON><PERSON>"}, "stress": {"title": "Comment est votre niveau de stress émotionnel récent ?", "low": "Faible", "moderate": "<PERSON><PERSON><PERSON><PERSON>", "high": "<PERSON><PERSON><PERSON>"}, "sleep": {"title": "<PERSON><PERSON>ien d'heures dormez-vous par jour ?", "unit": "heures", "short": "Je ne dors pas assez 😴", "good": "Je dors bien 😊", "long": "Je dors beaucoup 😌", "range_4": "4 heures", "range_8": "8 heures", "range_12": "12 heures"}, "additionalInfo": {"title": "Avez-vous d'autres informations de santé à ajouter ?", "description": "par exemple : autres maladies, allergies, médicaments à long terme, etc.", "placeholder": "Veuillez saisir ici..."}, "badHabits": {"title": "Nous avons tous de mauvaises habitudes. Quelles sont les vôtres ?", "smoking": "<PERSON><PERSON> fréque<PERSON>nt", "drinking": "<PERSON><PERSON> f<PERSON>", "stayingUpLate": "Se coucher tard fréquemment", "overeating": "Manger trop"}}}, "result": {"title": "Rapport d'Évaluation du Risque d'Slimmerplan", "description": "Évaluation personnalisée basée sur vos informations de santé", "reassess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "riskAssessment": "I. Évaluation du Niveau de Risque", "mainRiskFactors": "Principaux Facteurs de Risque", "bmi": "Indice IMC", "currentBloodPressure": "Tension Artérielle Actuelle", "systolic": "Systolique", "diastolic": "Diastolique", "healthAnalysis": "II. Ana<PERSON><PERSON> de l'État de Santé", "currentSymptoms": "(1) Symptômes actuels déduits", "managementAdvice": "Conseils de gestion", "noSymptoms": "<PERSON><PERSON><PERSON> symptôme notable détecté", "organDamageRisk": "Risque de lésions organiques", "lowRisk": "Risque faible", "moderateRisk": "Risque modéré", "highRisk": "Risque élevé", "possibleOrgans": "Organes potentiellement affectés", "possibleComplications": "Complications potentielles", "nonDrugPlan": "3. Plan de contrôle non médicamenteux", "dietAdjustment": "(1) Ajustements nutritionnels", "dietPlanLoading": "Personnalisation en cours selon votre région...", "customDietPlan": "Plan nutritionnel personnalisé", "dietAdvice": "Conseils alimentaires", "dietRestriction": "Restrictions alimentaires", "dietPlan": "Plan nutritionnel d<PERSON>lé", "exerciseIntervention": "(2) Programme d'exercice", "exercisePlanLoading": "Génération en cours...", "customExercisePlan": "Plan d'exercice personnalisé", "recommendedExercise": "Types d'exercice recommandés", "frequency": "<PERSON><PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON>", "precautions": "Précautions", "lifestyleAdjustment": "(3) Ajustements du mode de vie", "sleepManagement": "Gestion du sommeil", "stressManagement": "Gestion du stress", "habitAdjustment": "Modifications des habitudes", "followUpAdvice": "Conseils de suivi", "monitoringIndicators": "Indicateurs de surveillance", "checkupAdvice": "Recommandations de bilan", "emergencyIndicators": "Signes d'urgence nécessitant une consultation", "reassessConfirm": "⚠️ La réévaluation effacera tous vos plans personnalisés (nutrition et exercice). Confirmez-vous ?", "loading": "Chargement de votre évaluation..."}, "dietPlan": {"title": "Plan nutritionnel personnalisé pour slimmerplan", "subtitle": "Programme alimentaire individualisé basé sur l'évaluation des risques", "planTitle": "Plan nutritionnel pour slimmerplan", "planDescription": "Programme alimentaire individualisé basé sur l'évaluation des risques", "dailyMealPlan": "Planification des repas", "calendarTitle": "Calendrier nutritionnel", "calendarDescription": "Consultez le plan nutritionnel complet sur 30 jours et sélectionnez une date", "noDietPlanData": "Aucune donnée pour le jour {day}", "planStartsFromDay": "Plan débutant le {day}, durée {total} jours", "noMonthPlan": "Aucun plan ce mois-ci", "generatingProgress": "⏳ G<PERSON>éré {current}/30 jours", "weekDays": ["<PERSON><PERSON>", "<PERSON>n", "Mar", "<PERSON><PERSON>", "<PERSON><PERSON>", "Ven", "Sam"], "today": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "nutritionTips": {"saltReduction": "Ré<PERSON><PERSON> le sel diminue la tension", "balancedNutrition": "Une alimentation équilibrée aide à contrôler la tension", "regularMeals": "Des repas réguliers stabilisent la tension"}, "foodCategories": {"staples": "Féculents", "protein": "Protéines", "vegetables": "Lé<PERSON>es", "fruits": "Fruits", "dairy": "Produits laitiers", "nuts": "Noix"}, "dailyOverview": {"title": "Aperçu nutritionnel quotidien", "nutritionMatch": "Équilibre nutritionnel", "totalCalories": "Calories totales"}, "mealTimes": {"breakfast": "Petit-<PERSON><PERSON><PERSON><PERSON><PERSON>", "lunch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dinner": "<PERSON><PERSON><PERSON>", "snack": "Collation"}, "foodItems": {"oatmeal": "Flocons d'avoine", "boiledEgg": "<PERSON><PERSON> dur", "banana": "<PERSON><PERSON>", "apple": "<PERSON><PERSON>", "rice": "R<PERSON>", "chicken": "Poulet", "fish": "Poisson", "vegetables": "Légumes verts", "tofu": "Tofu", "milk": "<PERSON><PERSON>", "yogurt": "<PERSON><PERSON>"}, "nutritionInfo": {"calories": "Calories", "kcal": "kcal", "protein": "Protéines", "carbs": "Glucides", "fat": "Lipid<PERSON>", "fiber": "Fibres", "sodium": "Sodium", "potassium": "Potassium"}, "actions": {"deleteAndRegenerate": "Supprimer et regénérer", "backToResults": "Retour aux résultats", "downloadPlan": "Télécharger", "sharePlan": "Partager", "viewMore": "Voir plus", "refresh": "Actualiser"}, "timeFormats": {"morning": "7:30-8:00", "noon": "12:00-13:00", "evening": "18:30-19:30"}, "portions": {"times1": "×1", "times2": "×2", "times3": "×3", "serving": "portion", "cup": "tasse", "piece": "pi<PERSON><PERSON>", "bowl": "bol"}, "dayNumber": "Jour {day}", "monthDay": "{day}/{month}", "combinedWith": ", adapté à {country}", "regionalSpecialty": "Suggestion régionale", "clickTip": "📖 Cliquez sur un plat pour voir sa préparation | 🍚Féculents 🥩Protéines 🥬Légumes 🍎Fruits 🥜Autres", "deleteConfirm": "Confirmez la suppression du plan nutritionnel ? Un nouveau plan pourra être généré.", "deleteSuccess": "Plan supprimé - prêt pour une nouvelle génération", "deleteFailed": "Échec suppression :", "unknownError": "<PERSON><PERSON><PERSON> inconnue", "networkError": "Échec suppression : er<PERSON><PERSON>"}, "exercisePlan": {"refresh": "Actualiser", "calendarTitle": "<PERSON><PERSON><PERSON> d'exercice", "calendarDescription": "Consultez le programme complet sur 30 jours et sélectionnez une date", "planStartsFromDay": "Programme débutant le {day}, durée {total} jours", "noMonthPlan": "Aucun programme ce mois-ci - consultez d'autres mois", "generatingProgress": "⏳ Génération en cours : {current}/30 jours (actualisez pour voir la progression)", "weekDays": ["<PERSON><PERSON>", "<PERSON>n", "Mar", "<PERSON><PERSON>", "<PERSON><PERSON>", "Ven", "Sam"], "today": "<PERSON><PERSON>", "legend": {"today": "<PERSON><PERSON><PERSON>'hui", "hasExercisePlan": "Avec programme", "currentViewing": "Consultation"}, "noMonthExercise": "Aucun programme ce mois-ci", "noMonthExerciseDescription": "Utilisez les flèches pour consulter d'autres mois avec programme", "exerciseArrangement": "Programme d'exercice", "totalDuration": "Durée totale", "intensity": {"low": "Faible", "medium": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "high": "Élevée"}, "heartRateControl": "Contrôle du rythme cardiaque", "targetHeartRate": "Cible :", "calculationMethod": "Méthode de calcul :", "specificActions": "Exercices :", "duration": "Durée :", "sets": "Séries :", "reps": "Répétitions :", "precautions": "Précautions", "requiredEquipment": "Matériel nécessaire :", "dailyTips": "Conseil du jour", "exerciseGuidelines": "Recommandations", "exerciseContraindications": "Contre-indications", "progressiveAdvice": "Progression conseillée", "generatedTime": "<PERSON><PERSON><PERSON><PERSON> :", "clickTip": "🏃‍♂️ Cliquez sur un exercice pour des instructions détaillées", "monthPlanTitle": "Programme d'exercice", "deleteConfirm": "Confirmez la suppression du programme ? Un nouveau pourra être généré.", "deleteSuccess": "Programme supprimé - prêt pour une nouvelle génération", "deleteFailed": "Échec suppression :", "unknownError": "<PERSON><PERSON><PERSON> inconnue", "networkError": "Échec suppression : er<PERSON><PERSON>", "deleteAndRegenerate": "🗑️ Supprimer et regénérer", "backToResults": "← Retour aux résultats", "close": "<PERSON><PERSON><PERSON>", "exerciseActionDialog": {"intensity": {"low": "Faible", "medium": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "high": "Élevée", "unknown": "Inconnue"}, "actionDescription": "Description", "detailedSteps": "Étapes détaillées", "sets": "Séries", "reps": "Répétitions", "duration": "<PERSON><PERSON><PERSON>", "heartRateControl": "Contrôle du rythme cardiaque", "targetHeartRate": "Cible :", "calculationMethod": "Méthode de calcul :", "monitoringMethod": "Méthode de surveillance :", "tips": "Conseils", "precautions": "Précautions", "requiredEquipment": "Matériel nécessaire"}}, "toast": {"unlockFullDietPlan": "💡 <PERSON><PERSON><PERSON><PERSON><PERSON> le plan complet (30 jours)", "unlockFullDietPlanDesc": "Vous ne voyez que le 1er jour. Abonnez-vous pour accéder aux 30 jours personnalisés et atteindre vos objectifs santé !", "unlockFullExercisePlan": "💡 <PERSON><PERSON><PERSON><PERSON>quez le programme complet (30 jours)", "unlockFullExercisePlanDesc": "Vous ne voyez que le 1er jour. Abonnez-vous pour accéder aux 30 jours de conseils sportifs scientifiques !", "subscribe": "<PERSON>'abonner", "dietPlanUpdated": "✅ Plan nutritionnel mis à jour !", "dietPlanUpdatedDesc": "{count} jours ajoutés - total {total}/30 jours.", "exercisePlanUpdated": "✅ Programme d'exercice mis à jour !", "exercisePlanUpdatedDesc": "{count} jours ajoutés - total {total}/30 jours.", "stillGenerating": "⏳ Génération en cours...", "dietPlanGeneratingDesc": "Votre plan nutritionnel se génère - {completed}/30 jours prêts - revenez plus tard.", "exercisePlanGeneratingDesc": "Votre programme d'exercice se génère - {completed}/30 jours prêts - revenez plus tard.", "planIsLatest": "👍 Plan à jour", "dietPlanLatestDesc": "Votre plan nutritionnel est complet ({total}/30 jours).", "exercisePlanLatestDesc": "Votre programme d'exercice est complet ({total}/30 jours).", "noPlanToRefresh": "ℹ️ Aucun plan à actualiser", "noDietPlanDesc": "Gén<PERSON>rez d'abord un plan nutritionnel.", "noExercisePlanDesc": "<PERSON><PERSON><PERSON>rez d'abord un programme d'exercice.", "generateDietPlanFailed": "Échec de génération", "generateExercisePlanFailed": "Échec de génération", "networkError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "refreshFailed": "Échec d'actualisation - réessayez", "dietPlanGenerating": "📋 Génération en cours", "dietPlanGeneratingInProgress": "Votre plan nutritionnel se génère ({completed}/30 jours) - patientez ou actualisez plus tard !", "exercisePlanGenerating": "🏃‍♂️ Génération en cours", "exercisePlanGeneratingInProgress": "Votre programme d'exercice se génère ({completed}/30 jours) - patientez ou actualisez plus tard !", "assessmentFailed": "Échec d'évaluation", "networkErrorRetry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "riskLevels": {"low": "Faible", "moderate": "<PERSON><PERSON><PERSON><PERSON>", "high": "<PERSON><PERSON><PERSON>", "very_high": "<PERSON><PERSON><PERSON>", "unknown": "Inconnu"}, "foodCategories": {"staples": "Féculents", "protein": "Protéines", "vegetables": "Lé<PERSON>es", "fruits": "Fruits", "others": "Autres", "nutritionMatch": "🍽️ Équilibre nutritionnel", "viewRecipe": "Voir la recette"}, "foodRecipe": {"basicInfo": "Informations de base", "category": "Catégorie :", "quantity": "Quantité :", "calories": "Calories :", "ingredients": "Ingrédients", "steps": "Préparation", "tips": "Astuces", "nutritionValue": "<PERSON><PERSON>", "difficulty": {"easy": "Facile", "medium": "<PERSON><PERSON><PERSON>", "hard": "Difficile"}, "kcal": "kcal"}}