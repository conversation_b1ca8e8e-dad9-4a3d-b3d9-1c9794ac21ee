"use client"

import { useSession } from "next-auth/react"
import { useState, useEffect } from 'react'
import { useSubscription } from '@/hooks/use-subscription'
import CheckoutForm from './checkout-stripe'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { LoginDialog } from '@/components/auth/LoginDialog'
import styles from '@/styles/pricing/dialog.module.css'
import { Product } from '@/types/locales/pricing';
import { getDictionary, i18nNamespaces } from '@/i18n'
import { Pricing } from '@/types/locales/pricing';

interface Props {
    btnlabel: string;
    lang: string;
    mode: "payment" | "recurring";
    paymentTips: string;
    product: Product;
    currency: string;
}

// 简化的i18n数据
const getSimpleI18n = (lang: string) => ({
  auth: {
    login: {
      title: lang === 'zh' ? '登录账户' : 'Sign In',
      googleButton: lang === 'zh' ? '使用Google登录' : 'Sign in with Google',
      orDivider: lang === 'zh' ? '或使用邮箱继续' : 'or continue with email',
      emailLabel: lang === 'zh' ? '邮箱' : 'Email',
      emailPlaceholder: lang === 'zh' ? '请输入邮箱地址' : 'Enter your email',
      passwordLabel: lang === 'zh' ? '密码' : 'Password',
      passwordPlaceholder: lang === 'zh' ? '请输入密码（至少6个字符）' : 'Enter your password (min. 6 characters)',
      loginButton: lang === 'zh' ? '登录' : 'Sign In',
      registerLink: lang === 'zh' ? '还没有账户？' : "Don't have an account?",
      registerButton: lang === 'zh' ? '立即注册' : 'Sign Up',
      forgotPassword: lang === 'zh' ? '忘记密码？' : 'Forgot Password?'
    },
    register: {
      title: lang === 'zh' ? '创建账户' : 'Create Account',
      googleButton: lang === 'zh' ? '使用Google注册' : 'Sign up with Google',
      orDivider: lang === 'zh' ? '或使用邮箱注册' : 'or sign up with email',
      emailLabel: lang === 'zh' ? '邮箱' : 'Email',
      emailPlaceholder: lang === 'zh' ? '请输入邮箱地址' : 'Enter your email',
      passwordLabel: lang === 'zh' ? '密码' : 'Password',
      passwordPlaceholder: lang === 'zh' ? '请输入密码（至少6个字符）' : 'Enter your password (min. 6 characters)',
      firstNameLabel: lang === 'zh' ? '名字' : 'First Name',
      firstNamePlaceholder: lang === 'zh' ? '请输入名字' : 'Enter your first name',
      lastNameLabel: lang === 'zh' ? '姓氏' : 'Last Name',
      lastNamePlaceholder: lang === 'zh' ? '请输入姓氏' : 'Enter your last name',
      registerButton: lang === 'zh' ? '注册' : 'Sign Up',
      loginLink: lang === 'zh' ? '已有账户？' : 'Already have an account?',
      loginButton: lang === 'zh' ? '立即登录' : 'Sign In'
    },
    errors: {
      emailRequired: lang === 'zh' ? '请输入邮箱地址' : 'Email is required',
      emailInvalid: lang === 'zh' ? '请输入有效的邮箱地址' : 'Please enter a valid email',
      passwordRequired: lang === 'zh' ? '请输入密码' : 'Password is required',
      passwordLength: lang === 'zh' ? '密码至少需要6个字符' : 'Password must be at least 6 characters',
      firstNameRequired: lang === 'zh' ? '请输入名字' : 'First name is required',
      lastNameRequired: lang === 'zh' ? '请输入姓氏' : 'Last name is required',
      loginFailed: lang === 'zh' ? '登录失败，请检查邮箱和密码' : 'Login failed, please check your email and password',
      registerFailed: lang === 'zh' ? '注册失败，请稍后重试' : 'Registration failed, please try again',
      googleLoginFailed: lang === 'zh' ? 'Google登录失败，请稍后重试' : 'Google login failed, please try again',
      userNotFound: lang === 'zh' ? '用户不存在' : 'User does not exist',
      invalidCredentials: lang === 'zh' ? '邮箱或密码错误' : 'Invalid email or password',
      accountDisabled: lang === 'zh' ? '账户已被禁用，请联系管理员' : 'Account is disabled, please contact administrator',
      networkError: lang === 'zh' ? '网络错误，请稍后重试' : 'Network error, please try again later'
    },
    success: {
      welcomeBack: lang === 'zh' ? '欢迎回来！' : 'Welcome back!',
      welcomeNew: lang === 'zh' ? '欢迎注册！' : 'Welcome to register!'
    }
  }
});

export function PaymentButton({ btnlabel, lang, mode, product, currency, paymentTips }: Props) {
    const { data: session } = useSession()
    const { isSubscribed, userInfo } = useSubscription()
    const [isOpen, setIsOpen] = useState(false)
    const [showLoginDialog, setShowLoginDialog] = useState(false)
    const [pricingI18n, setPricingI18n] = useState<Pricing | null>(null)

    // 获取i18n数据
    const i18n = getSimpleI18n(lang);

    // 检查是否为订阅模式且用户已有订阅
    const isSubscriptionMode = mode === "recurring";
    const hasActiveSubscription = isSubscribed && isSubscriptionMode;

    // 加载pricing的i18n数据
    useEffect(() => {
        const loadPricingI18n = async () => {
            try {
                const data = await getDictionary<Pricing>(lang, i18nNamespaces.pricing);
                setPricingI18n(data);
            } catch (error) {
                console.error('Failed to load pricing i18n:', error);
            }
        };
        loadPricingI18n();
    }, [lang]);

    async function onClickHandler() {
        if (!session?.user) {
            // 如果未登录，显示登录对话框
            setShowLoginDialog(true);
            return
        }

        // 如果是订阅模式且用户已有活跃订阅，阻止操作
        if (hasActiveSubscription) {
            // 使用多语言支持的错误消息
            const errorMessage = pricingI18n?.errors?.activeSubscriptionExists ||
                (lang === 'zh' ? '您已有活跃的订阅计划，无法重复订阅。' : 'You already have an active subscription.');
            alert(errorMessage);
            return;
        }

        setIsOpen(true)
    }

    // 监听登录状态变化
    useEffect(() => {
        // 当用户登录成功时，自动打开支付对话框
        if (session?.user && showLoginDialog) {
            setShowLoginDialog(false);
            setIsOpen(true);
        }
    }, [session?.user, showLoginDialog]);

    return (
        <>
            <Dialog open={isOpen} onOpenChange={setIsOpen}>
                <DialogContent className={`sm:max-w-[1024px] border-primary-gold/30 ${styles.lightDialog}`}>
                    <DialogHeader className="flex flex-row items-start justify-between">
                        <DialogTitle className="flex-1">   
                            <span className="text-sm font-semibold text-primary-gold break-words">
                                {paymentTips}
                            </span>
                        </DialogTitle>
                    </DialogHeader>
                    {isOpen && <CheckoutForm priceId={product.priceId!}
                    className={styles.lightDialog} mode={mode}/>}
                </DialogContent>
            </Dialog>

            <Button
                onClick={onClickHandler}
                disabled={hasActiveSubscription}
                className={`w-full py-6 text-base font-semibold ${
                    hasActiveSubscription
                        ? 'bg-gray-400 hover:bg-gray-400 text-gray-600 cursor-not-allowed'
                        : 'bg-primary hover:bg-primary/90 text-white dark:bg-primary/80 dark:hover:bg-primary'
                }`}
                size="lg"
            >
                {hasActiveSubscription
                    ? (pricingI18n?.errors?.subscriptionAlreadyActive || (lang === 'zh' ? '已订阅' : 'Subscribed'))
                    : btnlabel
                }
            </Button>

            {/* 登录对话框 */}
            <LoginDialog
                isOpen={showLoginDialog}
                onClose={() => setShowLoginDialog(false)}
                lang={lang}
                i18n={i18n}
            />
        </>
    )
}